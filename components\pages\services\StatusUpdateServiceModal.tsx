'use client';

import { XIcon } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';

interface HideServiceModalProps {
    service: any;
    onClose: () => void;
    onConfirm: (reason: string) => void;
}

export default function StatusUpdateServiceModal({ service, onClose, onConfirm }: HideServiceModalProps) {
    const [reason, setReason] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    if (!service) return null;

    const handleSubmit = async () => {
        if (!reason.trim()) {
            return;
        }

        setIsSubmitting(true);
        try {
            onClose();
            await onConfirm(reason.trim());
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="mx-auto w-full max-w-md rounded-lg bg-white ">
            {/* Header */}
            <div className="mb-4 flex items-start justify-between">
                <h2 className="font-inter text-lg font-semibold text-[#2d2d2e]">Service Status Update</h2>
                <button onClick={onClose} className="flex h-6 w-6 items-center justify-center rounded-full text-xl text-gray-400 hover:bg-gray-100 hover:text-gray-600">
                    <XIcon />
                </button>
            </div>

            {/* Hide Icon */}
            <div className="mb-4 flex justify-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
                    <svg className="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.5 6.5m3.378 3.378a3 3 0 013.243-2.878m0 0L16.5 4.5m-3.379 2.878L9.878 9.878m4.242 4.242L16.5 16.5m-2.378-3.378a3 3 0 01-4.243-4.243m0 0L6.5 6.5"
                        />
                    </svg>
                </div>
            </div>

            {/* Content */}
            <div className="mb-6">
                <p className="mb-4 font-inter text-sm leading-relaxed text-[#636363]">
                    Service title <span className="font-medium text-[#2d2d2e]">{service.title}</span>
                </p>

                <div className="mb-4">
                    <label htmlFor="reason" className="mb-2 block text-sm font-medium text-gray-700">
                        Reason *
                    </label>
                    <textarea
                        id="reason"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Please provide a reason for hiding this service..."
                        className="w-full resize-none rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={4}
                        required
                    />
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
                <button
                    onClick={onClose}
                    disabled={isSubmitting}
                    className="rounded border border-gray-300 bg-gray-100 px-4 py-2 font-inter text-sm font-medium text-[#636363] transition-colors hover:bg-gray-200 disabled:opacity-50"
                >
                    Cancel
                </button>
                <button
                    onClick={handleSubmit}
                    disabled={!reason.trim() || isSubmitting}
                    className="rounded border border-[#2d2d2e] bg-[#2d2d2e] px-4 py-2 font-inter text-sm font-medium text-white transition-colors hover:bg-black disabled:opacity-50"
                >
                    {isSubmitting ? 'Submitting...' : 'Submit'}
                </button>
            </div>
        </div>
    );
}
