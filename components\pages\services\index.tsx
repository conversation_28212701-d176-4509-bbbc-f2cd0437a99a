'use server';

import { executeApiRequest } from '@/lib/helpers/actions.helpers';
import { REQUEST_TYPE } from '@/utils/types/action-helper.type';
import { ApiResponse } from '@/utils/types/utility.type';
import { ServicesResponse, ServiceFilterResponse, ServiceDetail } from './ServiceDTO';

export async function getServices(url: string): Promise<ApiResponse<ServicesResponse>> {
    return await executeApiRequest<ServicesResponse>({
        method: REQUEST_TYPE.get,
        url,
        withAuth: true,
        options: { cache: 'no-store' },
    });
}

export async function getServiceFilters(url: string): Promise<ApiResponse<ServiceFilterResponse>> {
    return await executeApiRequest<ServiceFilterResponse>({
        method: REQUEST_TYPE.get,
        url,
        withAuth: true,
        options: { cache: 'no-store' },
    });
}

export async function getServiceDetailsById(url: string): Promise<ApiResponse<ServiceDetail>> {
    return await executeApiRequest<ServiceDetail>({
        method: REQUEST_TYPE.get,
        url,
        withAuth: true,
        options: { cache: 'no-store' },
    });
}

export async function deleteServiceData(url: string): Promise<ApiResponse<null>> {
    return await executeApiRequest<null>({
        method: REQUEST_TYPE.delete,
        url,
        withAuth: true,
        options: { cache: 'no-store' },
    });
}

export async function saveServiceNotes(url: string, note: string): Promise<ApiResponse<null>> {
    return await executeApiRequest<null>({
        method: REQUEST_TYPE.post,
        url,
        withAuth: true,
        body: { note },
        options: { cache: 'no-store' },
    });
}

export async function changeServiceStatus(url: string, statusId: string, reason: string): Promise<ApiResponse<null>> {
    return await executeApiRequest<null>({
        method: REQUEST_TYPE.put,
        url,
        withAuth: true,
        body: { status: statusId, reason },
        options: { cache: 'no-store' },
    });
}
