import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface QuickActionsCardProps {
  onActionClick: (action: string) => void;
}

const QuickActionsCard: React.FC<QuickActionsCardProps> = ({ onActionClick }) => {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="text-xl font-semibold font-inter">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="h-12"
            onClick={() => onActionClick('support-tickets')}
          >
            Support Tickets
          </Button>
          <Button
            variant="outline"
            className="h-12 "
            onClick={() => onActionClick('manage-users')}
          >
            Manage Users
          </Button>
          <Button
            variant="outline"
            className="h-12"
            onClick={() => onActionClick('lead-management')}
          >
            Lead Management
          </Button>
          <Button
            variant="outline"
            className="h-12"
            onClick={() => onActionClick('reviews-management')}
          >
            Reviews Management
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActionsCard;
