import FilePreview from '@/components/reusable/FilePreview';
import React from 'react';

const DocumentDetail = ({ dataArray,type }: any) => {

    return (
        <div className="gap-4 md:gap-0">
            <div className="w-full">
                <div className="text-lg py-2 font-semibold leading-tight text-[#2d2d2e]">{type}: </div>
                <div className="flex w-full gap-5">
                   {
                    dataArray?.map((item: any, index: number) => {
                        return (
                            <div key={index} className="py-2 w-1/2">
                                <FilePreview frontFile={item }/>
                            </div>
                        );
                    })
                    }

                </div>
            </div>


        </div>
    );
};

export default DocumentDetail;
