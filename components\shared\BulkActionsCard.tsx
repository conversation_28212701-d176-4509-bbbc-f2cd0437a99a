import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface BulkAction {
  label: string;
  action: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  icon?: React.ElementType;
}

interface BulkActionsCardProps {
  selectedCount: number;
  actions: BulkAction[];
  onClearSelection?: () => void;
  className?: string;
}

const BulkActionsCard: React.FC<BulkActionsCardProps> = ({
  selectedCount,
  actions,
  onClearSelection,
  className = ''
}) => {
  if (selectedCount === 0) return null;

  return (
    <Card className={`border-blue-200 bg-blue-50 ${className}`}>
      <CardContent className="p-4">
          <div className="h-4"></div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 ">
            <span className="text-sm font-medium text-blue-900">
              {selectedCount} item{selectedCount > 1 ? 's' : ''} selected
            </span>
          </div>
          <div className="flex items-center gap-3">
            {actions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size="sm"
                  onClick={action.action}
                  className="h-8"
                >
                  {Icon && <Icon className="w-4 h-4 mr-2" />}
                  {action.label}
                </Button>
              );
            })}
            {onClearSelection && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
                className="h-8 w-8 p-0 text-blue-600 hover:text-blue-800 ml-3"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BulkActionsCard;
