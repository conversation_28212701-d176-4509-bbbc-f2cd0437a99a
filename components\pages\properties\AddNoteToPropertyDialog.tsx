import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Property } from '@/utils/types/property';

interface AddNoteToPropertyDialogProps {
  property: Property;
  children: React.ReactNode;
  onAddNote?: (propertyId: number, note: string) => void;
}

const AddNoteToPropertyDialog: React.FC<AddNoteToPropertyDialogProps> = ({
  property,
  children,
  onAddNote
}) => {
  const [note, setNote] = useState('');
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!note.trim()) return;

    // Call the callback if provided
    if (onAddNote) {
      onAddNote(property.id, note);
    }

    toast({
      title: "Note added",
      description: "Note has been successfully added to the property.",
    });
    setNote('');
  };

  const handleCancel = () => {
    setNote('');
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-lg">
        <AlertDialogHeader>
          <AlertDialogTitle>Add Note</AlertDialogTitle>
          <AlertDialogDescription>
            Add a note to property <strong>{property.title}</strong> by {property.agentName}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="space-y-4 p-6">
          <div className="space-y-2">
            <Label htmlFor="note">Note</Label>
            <Textarea
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Enter your note here..."
              rows={4}
              className="min-h-[100px] resize-none"
            />
          </div>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleSubmit}>
            Add Note
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default AddNoteToPropertyDialog;
