import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Property } from '@/utils/types/property';

interface AddNoteToPropertyDialogProps {
  property: Property;
  children?: React.ReactNode;
  onAddNote?: (propertyId: number, note: string) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const AddNoteToPropertyDialog: React.FC<AddNoteToPropertyDialogProps> = ({
  property,
  children,
  onAddNote,
  open,
  onOpenChange
}) => {
  const [note, setNote] = useState('');
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!note.trim()) return;

    // Call the callback if provided
    if (onAddNote) {
      onAddNote(property.id, note);
    }

    toast({
      title: "Note added",
      description: "Note has been successfully added to the property.",
    });
    setNote('');

    // Close the modal
    if (onOpenChange) {
      onOpenChange(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children && (
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
      )}
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Add Note</DialogTitle>
          <DialogDescription>
            Add a note to property <strong>{property.title || property.name || 'Unknown Property'}</strong>
            {property.agentName && ` by ${property.agencyName}`}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 p-6">
          <div className="space-y-2">
            <Label htmlFor="note">Note</Label>
            <Textarea
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Enter your note here..."
              rows={4}
              className="min-h-[100px] resize-none"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange?.(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Add Note
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddNoteToPropertyDialog;
