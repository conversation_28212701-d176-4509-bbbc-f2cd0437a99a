import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter } from 'lucide-react';

interface DashboardHeaderProps {
  applicationFilter: string;
  onApplicationFilterChange: (value: string) => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  applicationFilter,
  onApplicationFilterChange
}) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      {/* Dashboard Title and Description */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">Welcome to your admin dashboard overview</p>
      </div>

      {/* Application Filter */}
      <div className="flex items-center gap-2">
        <Filter className="w-4 h-4 text-gray-500" />
        <Select value={applicationFilter} onValueChange={onApplicationFilterChange}>
          <SelectTrigger className="w-48 min-w-48">
            <SelectValue placeholder="Filter applications" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Applications</SelectItem>
            <SelectItem value="Activated">Activated</SelectItem>
            <SelectItem value="Pending">Pending</SelectItem>
            <SelectItem value="Rejected">Rejected</SelectItem>
            <SelectItem value="Incomplete">Incomplete</SelectItem>
            <SelectItem value="Suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default DashboardHeader;
