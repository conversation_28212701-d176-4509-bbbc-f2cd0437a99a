import React from 'react';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Property } from '@/utils/types/property';

interface PropertyStatusBadgeProps {
  status: Property['status'];
  onStatusChange?: (newStatus: Property['status']) => void;
}

const PropertyStatusBadge: React.FC<PropertyStatusBadgeProps> = ({ status, onStatusChange }) => {
  // Normalize status to handle both lowercase and capitalized formats
  const normalizeStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  const variants: Record<string, { variant: 'default' | 'secondary' | 'destructive', icon: React.ComponentType<any> }> = {
    Available: { variant: 'default', icon: CheckCircle },
    Sold: { variant: 'default', icon: CheckCircle },
    Rented: { variant: 'default', icon: CheckCircle },
    Unpublished: { variant: 'secondary', icon: Eye },
    Blocked: { variant: 'destructive', icon: XCircle }
  };

  const statusOptions: string[] = ['Available', 'Sold', 'Rented', 'Unpublished', 'Blocked'];

  // Normalize the current status for lookup
  const normalizedStatus = status ? normalizeStatus(status) : '';
  const config = (normalizedStatus && variants[normalizedStatus]) || { variant: 'secondary' as const, icon: Eye };
  const Icon = config.icon;

  if (!onStatusChange) {
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {normalizedStatus || 'Unknown'}
      </Badge>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Badge variant={config.variant} className="flex items-center gap-1 hover:opacity-80 transition-opacity">
            <Icon className="w-3 h-3" />
            {normalizedStatus || 'Unknown'}
          </Badge>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white border shadow-lg z-50">
        {statusOptions.map((statusOption) => (
          <DropdownMenuItem
            key={statusOption}
            onClick={() => onStatusChange(statusOption)}
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-100"
          >
            {React.createElement(variants[statusOption].icon, { className: "w-4 h-4" })}
            {statusOption}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PropertyStatusBadge;
