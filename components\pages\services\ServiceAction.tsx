'use client';

import { useRef, useState, useEffect } from 'react';
import { Eye, MoreHorizontal, MessageSquare, Trash } from 'lucide-react';

export default function ServiceActions({ service, onView, onAddNote, onBlock, onDelete }: any) {
    const [open, setOpen] = useState(false);
    const [position, setPosition] = useState<'top' | 'bottom'>('bottom');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    // Close dropdown on outside click
    useEffect(() => {
        const handleClick = (e: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node) && buttonRef.current && !buttonRef.current.contains(e.target as Node)) {
                setOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClick);
        return () => document.removeEventListener('mousedown', handleClick);
    }, []);

    // Adjust dropdown position dynamically
    useEffect(() => {
        if (open && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            const spaceBelow = window.innerHeight - rect.bottom;
            const spaceAbove = rect.top;

            // assume dropdown height ~ 200px
            const estimatedHeight = 200;

            if (spaceBelow < estimatedHeight && spaceAbove > estimatedHeight) {
                setPosition('top');
            } else {
                setPosition('bottom');
            }
        }
    }, [open]);

    return (
        <td className="relative px-4 py-3 text-right text-sm font-medium">
            {/* Trigger */}
            <button ref={buttonRef} onClick={() => setOpen((prev) => !prev)} className="rounded p-2 text-gray-600 hover:bg-gray-100" title="Service Actions">
                <MoreHorizontal className="h-5 w-5" />
            </button>

            {/* Dropdown */}
            {open && (
                <div ref={dropdownRef} className={`absolute right-0 z-20 w-48 rounded-md border border-gray-200 bg-white shadow-lg ${position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'}`}>
                    <div className="py-1">
                        {/* View */}
                        <button
                            onClick={() => {
                                onView(service);
                                setOpen(false);
                            }}
                            className="flex w-full items-center px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                        >
                            <Eye className="mr-3 h-4 w-4" /> View Details
                        </button>

                        {/* Add Note */}
                        <button
                            onClick={() => {
                                onAddNote(service);
                                setOpen(false);
                            }}
                            className="flex w-full items-center px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                        >
                            <MessageSquare className="mr-3 h-4 w-4" /> Add Note
                        </button>

                        {/* Block / Activate Toggle */}
                        <button
                            onClick={() => {
                                onBlock(service);
                                setOpen(false);
                            }}
                            className={`flex w-full items-center px-4 py-2 text-left text-sm
                                ${service.statusname === 'Blocked' ? 'text-green-600 hover:bg-green-50' : 'text-red-600 hover:bg-red-50'}`}
                        >
                            {service.statusname.toLowerCase() == 'blocked' ? (
                                <>
                                    <svg className="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Unblock Service
                                </>
                            ) : (
                                <>
                                    <svg className="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.636 5.636l12.728 12.728" />
                                    </svg>
                                    Block Service
                                </>
                            )}
                        </button>

                        <div className="my-1 border-t border-gray-100"></div>

                        {/* Delete */}
                        <button
                            onClick={() => {
                                onDelete(service.id);
                                setOpen(false);
                            }}
                            className="flex w-full items-center px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50"
                        >
                            <Trash className="mr-3 h-4 w-4" /> Delete Service
                        </button>
                    </div>
                </div>
            )}
        </td>
    );
}
