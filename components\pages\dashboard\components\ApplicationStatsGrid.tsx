import React from 'react';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Building, Target, Filter } from 'lucide-react';

interface ApplicationStats {
  agentApplications: number;
  agencyApplications: number;
  advertisements: number;
}

interface ApplicationStatsGridProps {
  stats: ApplicationStats;
  applicationFilter: string;
  onCardClick: (cardType: string, role?: string) => void;
  getFilteredCount: () => number;
  getFilterLabel: () => string;
}

const ApplicationStatsGrid: React.FC<ApplicationStatsGridProps> = ({
  stats,
  applicationFilter,
  onCardClick,
  getFilteredCount,
  getFilterLabel
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 pt-5">
      <Card
        className="border-l-4 border-l-blue-500 cursor-pointer hover:shadow-lg transition-shadow p-6"
        onClick={() => onCardClick('agent', 'agent')}
      >
        <CardContent className="p-0">
          <div className="flex items-center justify-between mb-4">
            <CardTitle className="text-sm font-medium text-gray-600">Agent Applications</CardTitle>
            <Users className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-4xl font-bold text-gray-900 mb-2">
            {stats.agentApplications}
          </div>
          <Badge variant="secondary" className="text-xs">Active</Badge>
        </CardContent>
      </Card>

      <Card
        className="border-l-4 border-l-purple-500 cursor-pointer hover:shadow-lg transition-shadow p-6"
        onClick={() => onCardClick('agency', 'agency')}
      >
        <CardContent className="p-0">
          <div className="flex items-center justify-between mb-4">
            <CardTitle className="text-sm font-medium text-gray-600">Agency Applications</CardTitle>
            <Building className="w-5 h-5 text-purple-500" />
          </div>
          <div className="text-4xl font-bold text-gray-900 mb-2">
            {stats.agencyApplications}
          </div>
          <Badge variant="secondary" className="text-xs">Active</Badge>
        </CardContent>
      </Card>

      <Card
        className="border-l-4 border-l-orange-500 cursor-pointer hover:shadow-lg transition-shadow p-6"
        onClick={() => onCardClick('ads')}
      >
        <CardContent className="p-0">
          <div className="flex items-center justify-between mb-4">
            <CardTitle className="text-sm font-medium text-gray-600">Ads Applications</CardTitle>
            <Target className="w-5 h-5 text-orange-500" />
          </div>
          <div className="text-4xl font-bold text-gray-900 mb-2">
            {stats.advertisements}
          </div>
          <Badge variant="secondary" className="text-xs">Active</Badge>
        </CardContent>
      </Card>

      <Card
        className="border-l-4 border-l-green-500 cursor-pointer hover:shadow-lg transition-shadow p-6"
        onClick={() => onCardClick('applications')}
      >
        <CardContent className="p-0">
          <div className="flex items-center justify-between mb-4">
            <CardTitle className="text-sm font-medium text-gray-600">Total Applications</CardTitle>
            <Filter className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-4xl font-bold text-gray-900 mb-2">
            {getFilteredCount()}
          </div>
          <Badge
            variant={applicationFilter === 'Activated' ? 'default' :
                   applicationFilter === 'Pending' ? 'secondary' :
                   applicationFilter === 'Rejected' ? 'destructive' : 'outline'}
            className="text-xs"
          >
            {applicationFilter}
          </Badge>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApplicationStatsGrid;
