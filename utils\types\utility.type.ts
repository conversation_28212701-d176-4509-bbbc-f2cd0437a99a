/**
 * This type is used to get all the routes from the PAGE_ROUTES object
 */
export type AllRoutes<T> =
    T extends string
    ? T
    : T extends Record<string, unknown>
    ? { [K in keyof T]: AllRoutes<T[K]> }[keyof T]
    : never


/**
 * HTTP Status codes union type
 */
export type Status = 
    | 200 // OK
    | 201 // Created
    | 400 // Bad Request
    | 401 // Unauthorized
    | 403 // Forbidden
    | 404 // Not Found
    | 409 // Conflict
    | 422 // Unprocessable Entity
    | 500 // Internal Server Error
    | number; // Allow any other number for flexibility

/**
 * This type is used to get the response from server action used in Next JS
 */
export type ActionResponse<T = unknown> = {
    data: T,
    success: true,
} | {
    success: false,
    error: string,
}

export type SuccessApiResponse<T = unknown> = {
    success: true,
    data: T,
    status: Status,
    message: string,
}

export type ErrorApiResponse = {
    success: false,
    message: string,
    status: Status,
}

/**
 * This type is used to get the response from the API (Actual Backend API Response Type)
 */
export type ApiResponse<T = unknown> = SuccessApiResponse<T> | ErrorApiResponse

