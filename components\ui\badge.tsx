import * as React from "react"

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary" | "destructive" | "outline"
}

const badgeVariants = {
  default: "bg-[#1d7eb6] text-white hover:bg-[#37a2db]",
  secondary: "bg-[#f3f4f6] text-[#636363] hover:bg-[#e5e7eb]",
  destructive: "bg-[#dc2626] text-white hover:bg-[#b91c1c]",
  outline: "border border-[#e4e4e4] text-[#636363] hover:bg-[#f9fafb]"
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = "default", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${badgeVariants[variant]} ${className || ''}`}
        {...props}
      />
    )
  }
)
Badge.displayName = "Badge"

export { Badge }
