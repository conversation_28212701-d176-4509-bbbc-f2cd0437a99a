// utils/cookies.ts
export interface ParsedCookie {
    /** The cookie name (same as the key you asked for). */
    name: string;
    /** The cookie’s value. */
    value: string;
    /** Absolute expiry date (RFC 1123) or `undefined` for a session‑cookie. */
    expires?: Date;
    /** Max‑Age in seconds, if present. */
    maxAge?: number;
    path?: string;
    domain?: string;
    secure?: boolean;
    httpOnly?: boolean;
    sameSite?: "Strict" | "Lax" | "None";
}