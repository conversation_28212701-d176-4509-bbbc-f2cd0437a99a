export interface Property {
  id: number;
  name?: string; // API returns 'name' instead of 'title'
  title?: string; // Keep for backward compatibility
  description?: string;
  type?: string; // Will be derived from property_type_id
  price: string; // API returns as string
  size?: string; // API field name
  area?: string; // Keep for backward compatibility
  location_name?: string;
  property_type_name?: string;
  listing_type?: number; // API returns as number
  status_id?: number;
  status_name?: string;
  status?: string; // Keep for backward compatibility
  is_featured?: boolean | null;
  featured?: boolean; // Keep for backward compatibility
  is_verified?: boolean | null;
  verified?: boolean; // Keep for backward compatibility
  expiry_date?: string;
  expiryDate?: string; // Keep for backward compatibility
  slug?: string;
  meta_title?: string;
  images?: PropertyImage[];
  agentName?: string;
  agencyName?: string;
  bedrooms?: number;
  bathrooms?: number;
  yearBuilt?: number;
  features?: string[];
  emirate?: string;
  createdAt?: string;
  updatedAt?: string;
  views?: number;
  notes?: PropertyNote[];
}

export interface PropertyImage {
  id: number;
  url: string;
}

export interface PropertyNote {
  id: string;
  propertyId: string;
  note: string;
  createdBy: string;
  createdAt: string;
}

export interface PropertyFilter {
  search: string;
  status: string;
  type: string;
  location: string;
  listingType: string;
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface PropertyStats {
  totalProperties: number;
  activeProperties: number;
  pendingProperties: number;
  blockedProperties: number;
}

export interface PropertyPagination {
  total: number;
  totalPages: number;
  currentPage: number;
  perPage: number;
  hasNext?: boolean;
  hasPrev?: boolean;
  page?: number; // Keep for backward compatibility
  pageSize?: number; // Keep for backward compatibility
}

export interface PropertiesResponse {
  status: number;
  success: boolean;
  message: string;
  data: {
    properties: Property[];
    pagination: PropertyPagination;
  };
}

export type PropertySortField = 'title' | 'price' | 'location' | 'expiryDate' | 'createdAt';
export type SortOrder = 'asc' | 'desc';
