import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Property } from '@/utils/types/property';

interface PropertyTypeBadgeProps {
  type: Property['type'];
}

const PropertyTypeBadge: React.FC<PropertyTypeBadgeProps> = ({ type }) => {
  const colors: Record<string, string> = {
    residential: 'bg-blue-100 text-blue-800',
    commercial: 'bg-green-100 text-green-800',
    land: 'bg-yellow-100 text-yellow-800',
    rental: 'bg-purple-100 text-purple-800'
  };

  return (
      <Badge variant="secondary" className={type && colors[type] ? colors[type] : 'bg-gray-100 text-gray-800'}>
      {type ? type.charAt(0).toUpperCase() + type.slice(1) : 'Unknown'}
    </Badge>
  );
};

export default PropertyTypeBadge;
