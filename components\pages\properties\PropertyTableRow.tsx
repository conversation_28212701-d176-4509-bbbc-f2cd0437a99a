import React, { useState } from 'react';
import { MoreHorizontal, Eye, Edit, Ban, Trash2, Star, CheckCircle, MessageSquare } from 'lucide-react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import PropertyStatusBadge from './PropertyStatusBadge';
import PropertyTypeBadge from './PropertyTypeBadge';
import PropertyActionReasonDialog from './PropertyActionReasonDialog';
import AddNoteToPropertyDialog from './AddNoteToPropertyDialog';
import { Property } from '@/utils/types/property';

interface PropertyTableRowProps {
    property: Property;
    isSelected: boolean;
    onSelect: () => void;
    onDelete: () => void;
    onBlock: () => void;
    onStatusChange: (status: Property['status'], reason?: string) => void;
    onAddNote?: (propertyId: number, note: string) => void;
}

const PropertyTableRow: React.FC<PropertyTableRowProps> = ({ property, isSelected, onSelect, onDelete, onBlock, onStatusChange, onAddNote }) => {
    const [reasonDialogOpen, setReasonDialogOpen] = useState(false);
    const [actionType, setActionType] = useState<'block' | 'unpublish'>('block');
    const [addNoteDialogOpen, setAddNoteDialogOpen] = useState(false);

    const formatPrice = (price: string | number) => {
        const numPrice = typeof price === 'string' ? parseFloat(price) : price;
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'AED',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(numPrice);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const handleBlockClick = () => {
        setActionType('block');
        setReasonDialogOpen(true);
    };

    const handleUnpublishClick = () => {
        setActionType('unpublish');
        setReasonDialogOpen(true);
    };

    const handleReasonConfirm = (reason: string) => {
        console.log('handleReasonConfirm called with:', {
            actionType,
            reason,
            reasonTrimmed: reason.trim()
        });

        if (actionType === 'block') {
            onStatusChange('blocked', reason);
        } else {
            onStatusChange('unpublished', reason);
        }
        setReasonDialogOpen(false);
    };

    const handleAddNoteClick = () => {
        setAddNoteDialogOpen(true);
    };

    const handleAddNoteConfirm = (propertyId: number, note: string) => {
        if (onAddNote) {
            onAddNote(propertyId, note);
        }
        setAddNoteDialogOpen(false);
    };

    return (
        <>
            <TableRow>
                <TableCell>
                    <Checkbox checked={isSelected} onCheckedChange={onSelect} />
                </TableCell>
                <TableCell>
                    <div className="flex items-center gap-3">
                        <img src={property.images?.[0]?.url || '/default.png'} alt={property.name || property.title || 'Property'} className="h-12 w-12 rounded-lg object-cover" />
                        <div>
                            <div className="flex items-center gap-2 font-medium">
                                {property.name || property.title || 'Untitled Property'}
                                {property.featured && <Star className="h-4 w-4 fill-current text-yellow-500" />}
                                {property.verified && <CheckCircle className="h-4 w-4 text-green-500" />}
                            </div>
                            <div className="text-sm text-gray-500">ID: {property.id}</div>
                        </div>
                    </div>
                </TableCell>
                <TableCell>
                    <PropertyTypeBadge type={property.type} />
                </TableCell>
                <TableCell className="font-semibold">{formatPrice(property.price)}</TableCell>
                <TableCell>{property.location_name || 'N/A'}</TableCell>
                <TableCell>
                    <div>
                        <div className="font-medium">{property.agencyName}</div>
                    </div>
                </TableCell>
                <TableCell>
                    <PropertyStatusBadge status={property.status} onStatusChange={onStatusChange} />
                </TableCell>
                <TableCell>
                    <div className="text-sm">{property.expiryDate ? formatDate(property.expiryDate) : 'N/A'}</div>
                </TableCell>
                <TableCell>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-auto px-2 text-black hover:bg-gray-50">
                                •••
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="z-[9998] min-w-[200px] border bg-white shadow-lg">
                            <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                            </DropdownMenuItem>
                            {/* <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Property
                            </DropdownMenuItem> */}
                            <DropdownMenuItem onClick={handleUnpublishClick}>
                                <Ban className="mr-2 h-4 w-4" />
                                Unpublish Property
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={handleBlockClick}>
                                <Ban className="mr-2 h-4 w-4" />
                                Block Property
                            </DropdownMenuItem>
                            {onAddNote && (
                                <DropdownMenuItem onClick={handleAddNoteClick}>
                                    <MessageSquare className="mr-2 h-4 w-4" />
                                    Add Note
                                </DropdownMenuItem>
                            )}
                            <AlertDialog>
                                <AlertDialogTrigger asChild>
                                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete Property
                                    </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                        <AlertDialogTitle>Delete Property</AlertDialogTitle>
                                        <AlertDialogDescription>
                                            Are you sure you want to delete "{property.name || property.title || 'this property'}"? This action cannot be undone.
                                        </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction onClick={onDelete}>Delete</AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </TableCell>
            </TableRow>

            <PropertyActionReasonDialog isOpen={reasonDialogOpen} onClose={() => setReasonDialogOpen(false)} onConfirm={handleReasonConfirm} property={property} actionType={actionType} />

            {onAddNote && addNoteDialogOpen && (
                <AddNoteToPropertyDialog
                    property={property}
                    onAddNote={handleAddNoteConfirm}
                    open={addNoteDialogOpen}
                    onOpenChange={setAddNoteDialogOpen}
                />
            )}
        </>
    );
};

export default PropertyTableRow;
