import { PROPERTIES_API, PROPERTY_DELETE_API, PROPERTY_ADD_NOTE_API } from '@/app/lib/apiRoutes';
import { Property, PropertyPagination, PropertiesResponse } from '@/utils/types/property';

interface ApiProperty {
    id: number;
    name: string;
    price: string;
    size: string;
    location_id: number;
    location_name: string;
    property_type_name: string;
    agency_name: string;
    listing_type: number;
    status_id: number;
    status_name: string;
    is_featured: boolean | null;
    is_verified: boolean | null;
    expiry_date: string;
    slug: string;
    meta_title: string;
    images: Array<{
        id: number;
        url: string;
    }>;
}

interface ApiPropertiesResponse {
    status: number;
    success: boolean;
    message: string;
    data: {
        properties: ApiProperty[];
        pagination: {
            total: number;
            totalPages: number;
            currentPage: number;
            perPage: number;
        };
        statusCounts: Array<{
            status_id: number;
            status_name: string;
            count: number;
        }>;
    };
}

class PropertyService {
    private getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
        };
    }

    private transformProperty(apiProperty: ApiProperty): Property {
        return {
            id: apiProperty.id,
            name: apiProperty.name,
            title: apiProperty.name, // Map name to title for compatibility
            price: apiProperty.price,
            size: apiProperty.size,
            area: apiProperty.size, // Map size to area for compatibility
            listing_type: apiProperty.listing_type,
            status_id: apiProperty.status_id,
            status_name: apiProperty.status_name,
            status: apiProperty.status_name, // Map status_name to status for compatibility
            is_featured: apiProperty.is_featured,
            featured: apiProperty.is_featured || false, // Map is_featured to featured
            is_verified: apiProperty.is_verified,
            verified: apiProperty.is_verified || false, // Map is_verified to verified
            expiry_date: apiProperty.expiry_date,
            expiryDate: apiProperty.expiry_date, // Map expiry_date to expiryDate
            slug: apiProperty.slug,
            meta_title: apiProperty.meta_title,
            images: apiProperty.images,
            // Set default values for fields not in API
            type: apiProperty.property_type_name, // Default type
            location_name: apiProperty.location_name,
            agentName: 'N/A', // Default agent name
            agencyName: apiProperty.agency_name, // Default agency name
        };
    }

    private transformPagination(apiPagination: any): PropertyPagination {
        return {
            total: apiPagination.total,
            totalPages: apiPagination.totalPages,
            currentPage: apiPagination.currentPage,
            perPage: apiPagination.perPage,
            // Add compatibility fields
            page: apiPagination.currentPage,
            pageSize: apiPagination.perPage,
            hasNext: apiPagination.currentPage < apiPagination.totalPages,
            hasPrev: apiPagination.currentPage > 1,
        };
    }

    async getProperties(
        page: number = 1,
        pageSize: number = 10,
        status?: string,
        propertyTypeId?: string,
        locationId?: string,
        listingType?: string
    ): Promise<PropertiesResponse> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            if (status) params.append('status', status);
            if (propertyTypeId) params.append('propertyTypeId', propertyTypeId);
            if (locationId) params.append('locationId', locationId);
            if (listingType) params.append('listingType', listingType);

            const response = await fetch(`${PROPERTIES_API}?${params.toString()}`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const apiData: ApiPropertiesResponse = await response.json();

            // Transform the response
            const transformedProperties = apiData.data.properties.map(this.transformProperty);
            const transformedPagination = this.transformPagination(apiData.data.pagination);

            const result: PropertiesResponse = {
                status: apiData.status,
                success: apiData.success,
                message: apiData.message,
                data: {
                    properties: transformedProperties,
                    pagination: transformedPagination,
                },
            };

            return result;
        } catch (error) {
            console.error('Error fetching properties:', error);
            throw error;
        }
    }

    async deleteProperty(id: number): Promise<{ status: number; success: boolean; message: string }> {
        try {
            const response = await fetch(PROPERTY_DELETE_API(id), {
                method: 'DELETE',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error deleting property:', error);
            throw error;
        }
    }

    async addNote(id: number, note: string): Promise<{ status: number; success: boolean; message: string; data?: any }> {
        try {
            const formData = new FormData();
            formData.append('note', note);

            const response = await fetch(PROPERTY_ADD_NOTE_API(id), {
                method: 'POST',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error adding note to property:', error);
            throw error;
        }
    }
}

// Refactor updatePropertyStatus to be standalone
export async function updatePropertyStatus(propertyId: string, status: string, reason?: string): Promise<{ success: boolean }> {
    try {
        // Use FormData to match the API specification
        const formData = new FormData();
        formData.append('status', status);

        // Add reason if provided (required for blocking)
        if (reason) {
            formData.append('reason', reason);
        }

        // Debug logging
        console.log('Sending property status update:', {
            propertyId,
            status,
            reason,
            formDataEntries: Array.from(formData.entries())
        });

        const response = await fetch(`${PROPERTIES_API}/${propertyId}/status`, {
            method: 'PUT',
            credentials: 'include',
            body: formData,
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Failed to update property status:', errorText);
            return { success: false };
        }

        const responseData = await response.json();
        console.log('Property status update response:', responseData);
        return { success: true };
    } catch (error) {
        console.error('Error updating property status:', error);
        return { success: false };
    }
}

// Ensure only one default export
export default PropertyService;
