import { FC } from 'react';
import { SearchIcon } from '../icon/Icon';

interface SearchInputProps {
    placeholder?: string;
    value?: string;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    classes?: string;
}

const SearchInput: FC<SearchInputProps> = ({ placeholder = 'Search', value, onChange, classes }) => {
    return (
        <div className={` inline-flex h-14  w-full items-start ${classes}`}>
            <div className="inline-flex h-14 w-full items-center gap-3 rounded-lg border border-[#e4e4e4] px-5 py-4">
                <div className="flex h-6 w-6 items-center justify-center">
                    <SearchIcon />
                </div>
                <input
                    type="text"
                    placeholder={placeholder}
                    value={value}
                    onChange={onChange}
                    className="w-full bg-transparent font-inter text-base font-normal leading-normal text-black outline-none placeholder:text-neutral-400"
                />
            </div>
        </div>
    );
};

export default SearchInput;
