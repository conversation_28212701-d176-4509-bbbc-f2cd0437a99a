import React from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';

interface PropertyTableHeadersProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSort: (field: string) => void;
}

const PropertyTableHeaders = ({ sortBy, sortOrder, onSort }: PropertyTableHeadersProps) => {
  const SortableHeader = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <button
      className="flex items-center gap-1 font-medium hover:text-blue-600 transition-colors"
      onClick={() => onSort(field)}
    >
      {children}
      {sortBy === field && (
        sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
      )}
    </button>
  );

  return [
    <SortableHeader field="title">Property</SortableHeader>,
    'Type',
    <SortableHeader field="price">Price</SortableHeader>,
    <SortableHeader field="location">Location</SortableHeader>,
    'Agent/Agency',
    'Status',
    <SortableHeader field="expiryDate">Expiry Date</SortableHeader>,
    'Actions'
  ];
};

export default PropertyTableHeaders;
