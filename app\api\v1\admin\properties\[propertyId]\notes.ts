import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuthToken } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { propertyId } = req.query;

  if (!propertyId || typeof propertyId !== 'string') {
    return res.status(400).json({ message: 'Invalid property ID' });
  }

  try {
    // Check authentication
    const adminAuthToken = req.cookies.adminAuthToken;
    if (!adminAuthToken || !verifyAdminAuthToken(adminAuthToken)) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Handle both form data and JSON data
    let note: string;

    if (req.headers['content-type']?.includes('application/x-www-form-urlencoded') ||
        req.headers['content-type']?.includes('multipart/form-data')) {
      // Handle form data (as per cURL example)
      note = req.body.note;
    } else {
      // Handle JSON data
      note = req.body.note;
    }

    if (!note || !note.trim()) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    // For now, we'll just return success since we don't have the actual backend
    // In a real implementation, this would save to the database
    return res.status(200).json({
      success: true,
      message: 'Note added successfully',
      data: {
        id: Date.now().toString(),
        propertyId: propertyId,
        note: note.trim(),
        createdBy: 'admin',
        createdAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error adding note to property:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
