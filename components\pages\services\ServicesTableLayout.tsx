'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import ServiceTable from './ServiceTable';

const ServiceTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Service Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Services' }]}
            />

            <div className="px-4">
                <ServiceTable/>
            </div>
        </DefaultPageLayout>
    );
};

export default ServiceTableLayout;

