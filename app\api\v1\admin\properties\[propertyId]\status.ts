import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuthToken } from '@/lib/auth';
import { updatePropertyStatus } from '@/lib/propertyService';

const validStatuses = ['available', 'sold', 'rented', 'unpublished', 'blocked'];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { propertyId } = req.query;

  // Handle both JSON and form data
  let status: string;
  let reason: string | undefined;

  console.log('Request headers:', req.headers);
  console.log('Request body:', req.body);

  if (req.headers['content-type']?.includes('application/json')) {
    // Handle JSON data
    status = req.body.status;
    reason = req.body.reason;
  } else {
    // Handle form data
    status = req.body.status;
    reason = req.body.reason;
  }

  console.log('Parsed values:', { status, reason });

  if (!propertyId || typeof propertyId !== 'string') {
    return res.status(400).json({ message: 'Invalid property ID' });
  }

  if (!status || !validStatuses.includes(status)) {
    return res.status(400).json({ message: 'Invalid status value' });
  }

  // Validate that reason is provided when blocking
  if (status === 'blocked' && (!reason || !reason.trim())) {
    return res.status(400).json({ message: 'Reason is required when blocking a property' });
  }

  try {
    const adminAuthToken = req.cookies.adminAuthToken;
    if (!adminAuthToken || !verifyAdminAuthToken(adminAuthToken)) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // For now, we'll just return success since we don't have the actual backend
    // In a real implementation, this would save to the database
    console.log('Property status update:', {
      propertyId,
      status,
      reason: reason || 'No reason provided',
      timestamp: new Date().toISOString()
    });

    return res.status(200).json({
      success: true,
      message: 'Property status updated successfully',
      data: {
        propertyId,
        status,
        reason: reason || null,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating property status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
