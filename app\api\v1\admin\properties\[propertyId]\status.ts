import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuthToken } from '@/lib/auth';
import { updatePropertyStatus } from '@/lib/propertyService';

const validStatuses = ['available', 'sold', 'rented', 'unpublished', 'blocked'];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { propertyId } = req.query;
  const { status } = req.body;

  if (!propertyId || typeof propertyId !== 'string') {
    return res.status(400).json({ message: 'Invalid property ID' });
  }

  if (!status || !validStatuses.includes(status)) {
    return res.status(400).json({ message: 'Invalid status value' });
  }

  try {
    const adminAuthToken = req.cookies.adminAuthToken;
    if (!adminAuthToken || !verifyAdminAuthToken(adminAuthToken)) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const result = await updatePropertyStatus(propertyId, status);
    if (result.success) {
      return res.status(200).json({ message: 'Property status updated successfully' });
    } else {
      return res.status(500).json({ message: 'Failed to update property status' });
    }
  } catch (error) {
    console.error('Error updating property status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
