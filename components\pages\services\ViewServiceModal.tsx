'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import Loading from '@/components/layouts/loading';
import { showMessage } from '@/app/lib/Alert';
import { getServiceDetailsById } from '.';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { ServiceDetail } from './ServiceDTO';

interface ViewServiceModalProps {
    serviceId: number | null;
    onClose: () => void;
}

export default function ViewServiceModal({ serviceId, onClose }: ViewServiceModalProps) {
    const [service, setService] = useState<ServiceDetail | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (!serviceId) return;

        const fetchService = async () => {
            try {
                setLoading(true);
                const payload = await getServiceDetailsById(`${API_ENDPOINTS.SERVICES_URL}/${serviceId}`);

                if (!payload.success) return;
                console.log(payload.data);

                const json = payload;
                if (!json.success) throw new Error(json.message || 'Failed to fetch');

                setService(json.data);
            } catch (err) {
                console.error(err);
                showMessage('Failed to load service details', 'error');
            } finally {
                setLoading(false);
            }
        };

        fetchService();
    }, [serviceId]);

    if (!serviceId) return null;

    const getStatusBadge = (status: string) => {
        const statusClasses: Record<string, string> = {
            Activated: 'bg-gray-900 text-white',
            Pending: 'bg-yellow-100 text-yellow-700',
            Blocked: 'bg-red-500 text-white',
            Paused: 'bg-orange-500 text-white',
        };
        return <span className={`rounded-full px-3 py-1 text-xs font-medium ${statusClasses[status] || 'bg-gray-200 text-gray-700'}`}>{status}</span>;
    };

    const formatMoney = (n: string | number) => new Intl.NumberFormat('en-AE', { style: 'currency', currency: 'AED' }).format(Number(n));

    const formatDate = (iso: string) => new Date(iso).toLocaleString('en-GB', { year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' });

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
            <div className="max-h-[90vh] w-full max-w-3xl overflow-y-auto rounded-lg bg-white shadow-xl">
                <div className="p-6">
                    {/* Header */}
                    <div className="mb-6 flex items-start justify-between">
                        <h2 className="text-xl font-semibold text-gray-800">Service Details</h2>
                        <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                            ✕
                        </button>
                    </div>

                    {loading ? (
                        <div className="flex justify-center py-10">
                            <Loading fullscreen={false} />
                        </div>
                    ) : service ? (
                        <>
                            {/* Status */}
                            <div className="mb-6">{getStatusBadge(service.statusname)}</div>

                            {/* Title & Image */}
                            <div className="mb-6 flex items-center gap-4">
                                {service.images?.length > 0 ? (
                                    <Image
                                        src={`${process.env.NEXT_PUBLIC_DOCUMENTS_BASE_URL}${service.images[0].url}`}
                                        alt={service.title}
                                        width={64}
                                        height={64}
                                        className="rounded-full object-cover"
                                    />
                                ) : (
                                    <Image
                                        src={`https://ui-avatars.com/api/?name=${encodeURIComponent(service.title.substring(0, 2))}&background=random&color=fff&size=64`}
                                        alt={service.title}
                                        width={64}
                                        height={64}
                                        className="rounded-full object-cover"
                                    />
                                )}
                                <div>
                                    <h3 className="text-2xl font-semibold text-gray-900">{service.title}</h3>
                                    <p className="text-sm text-gray-500">ID: srv-{service.id}</p>
                                </div>
                            </div>

                            {/* Info Grid */}
                            <div className="mb-6 grid grid-cols-2 gap-6">
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Type</h4>
                                    <p>{service.services[0]?.label ?? '-'}</p>
                                </div>
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Location</h4>
                                    <p>{service.locations[0]?.label ?? '-'}</p>
                                </div>
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Pricing</h4>
                                    <p>{service.isFree ? 'Free' : formatMoney(service.price)}</p>
                                </div>
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Duration</h4>
                                    <p>
                                        {service.duration} {service.durationtype?.name ?? ''}
                                    </p>
                                </div>
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Website</h4>
                                    <a href={service.websiteUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                        {service.websiteUrl ?? '-'}
                                    </a>
                                </div>
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Special Offer</h4>
                                    <p>{service.specialOffer ?? '-'}</p>
                                </div>
                                <div>
                                    <h4 className="text-sm font-medium text-gray-500">Created On</h4>
                                    <p>{formatDate(service.createdOn)}</p>
                                </div>
                                {service.modifiedOn && (
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-500">Last Modified</h4>
                                        <p>{formatDate(service.modifiedOn)}</p>
                                    </div>
                                )}
                            </div>

                            {/* Description */}
                            <div className="mb-6">
                                <h4 className="mb-2 text-sm font-medium text-gray-500">Description</h4>
                                <p className="text-sm leading-relaxed text-gray-700">{service.description}</p>
                            </div>

                            {/* Profile */}
                            <div>
                                <h4 className="mb-2 text-sm font-medium text-gray-500">Agent/Agency</h4>
                                <div className="flex items-center gap-3">
                                    {service.profile?.profileImage ? (
                                        <Image src={service.profile.profileImage} alt={service.profile.firstName} width={48} height={48} className="rounded-full object-cover" />
                                    ) : (
                                        <Image
                                            src={`https://ui-avatars.com/api/?name=${encodeURIComponent(service.profile.firstName.substring(0, 2))}&background=random&color=fff&size=48`}
                                            alt={service.profile.firstName}
                                            width={48}
                                            height={48}
                                            className="rounded-full object-cover"
                                        />
                                    )}
                                    <div>
                                        <p className="font-medium">
                                            {service.profile.firstName} {service.profile.lastName}
                                        </p>
                                        <p className="text-sm text-gray-500">{service.profile.email}</p>
                                    </div>
                                </div>
                            </div>
                        </>
                    ) : (
                        <p className="text-center text-sm text-gray-500">No details found.</p>
                    )}
                </div>
            </div>
        </div>
    );
}
