import React from 'react';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';

interface EmptyState {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface DataTableProps {
  headers: React.ReactNode[];
  children: React.ReactNode;
  selectedItems: string[];
  totalItems: number;
  onSelectAll: (checked: boolean) => void;
  emptyState?: EmptyState;
  className?: string;
}

const DataTable: React.FC<DataTableProps> = ({
  headers,
  children,
  selectedItems,
  totalItems,
  onSelectAll,
  emptyState,
  className = ''
}) => {
  const isAllSelected = selectedItems.length === totalItems && totalItems > 0;
  const isIndeterminate = selectedItems.length > 0 && selectedItems.length < totalItems;

  const handleSelectAll = (checked: boolean) => {
    onSelectAll(checked);
  };

  if (totalItems === 0 && emptyState) {
    return (
      <div className={`bg-white rounded-lg border ${className}`}>
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          {emptyState.icon}
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{emptyState.title}</h3>
          <p className="text-gray-500 max-w-md">{emptyState.description}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border ${className}`}>
      <div className="overflow-x-auto overflow-y-visible">
        <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                indeterminate={isIndeterminate}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            {headers.map((header, index) => (
              <TableHead key={index}>{header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {children}
        </TableBody>
      </Table>
      </div>
    </div>
  );
};

export default DataTable;
