export interface ServiceImage {
    id: number;
    serviceId: number;
    url: string;
}

export interface ServiceType {
    value: number;
    label: string;
}

export interface ServiceLocation {
    value: number;
    label: string;
}

export interface DurationType {
    id: number;
    name: string;
}

export interface Profile {
    id: number;
    firstName: string;
    middleName?: string;
    lastName?: string;
    phone?: string;
    email: string;
    profileImage?: string | null;
}

export interface Service {
    id: number;
    profileId: number;
    agencyId: number | null;
    currencyId: number | null;
    isRemote: boolean;
    isFree: boolean;
    price: string;
    title: string;
    experience: number | null;
    duration: number;
    isDeleted: boolean;
    specialOffer: string | null;
    websiteUrl: string | null;
    description: string;
    address: string | null;
    views: number;
    statusId: number;
    createdBy: number;
    createdOn: string;
    modifiedBy?: number | null;
    modifiedOn?: string | null;
    statusname: string;
    images: ServiceImage[];
    services: ServiceType[];
    locations: ServiceLocation[];
    durationtype: DurationType;
    profile: Profile;
}

export interface ServicesResponse {
    items: Service[];
    counts: {
        total_services: number;
        services_by_status: {
            statusId: number;
            status: string;
            count: number;
        }[];
    };
    pagination: {
        page: number;
        limit: number;
        total: number;
    };
}

export type ServiceFilterResponse = {
    durations: {
        id: number;
        name: string;
        children: {
            id: number;
            name: string;
            parentId: number;
        }[];
    }[];
    locations: Locations[];
    status: Status[];
};

export interface Status {
    id: number;
    name: string;
    parentId: number | null;
    description: string;
    createdOn: string; // ISO datetime string
    modifiedOn: string | null;
}

export interface Locations {
    id: number;
    name: string;
    local: string;
    typeId: number;
    parentId: number | null;
    postalCode: string | null;
    statusId: number;
    createdOn: string; // ISO datetime string
    modifiedOn: string | null;
}

export interface ServiceDetail {
    id: number;
    title: string;
    description: string;
    websiteUrl?: string;
    specialOffer?: string;
    price: string;
    isFree: boolean;
    statusname: string;
    createdOn: string;
    modifiedOn?: string;
    duration?: number;
    durationtype?: { id: number; name: string };
    services: { value: number; label: string }[];
    locations: { value: number; label: string }[];
    images: { id: number; url: string }[];
    profile: {
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        profileImage?: string;
    };
}
