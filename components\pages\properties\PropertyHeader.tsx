import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, CheckCircle, Clock, XCircle } from 'lucide-react';

interface PropertyHeaderProps {
    totalProperties: number;
    activeProperties: number;
    pendingProperties: number;
    blockedProperties: number;
}

const PropertyHeader: React.FC<PropertyHeaderProps> = ({ totalProperties, activeProperties, pendingProperties, blockedProperties }) => {
    const stats = [
        {
            title: 'Total Properties',
            value: totalProperties,
            icon: Building,
            color: 'text-blue-600',
        },
        {
            title: 'Active Properties',
            value: activeProperties,
            icon: CheckCircle,
            color: 'text-green-600',
        },
        {
            title: 'Pending Properties',
            value: pendingProperties,
            icon: Clock,
            color: 'text-yellow-600',
        },
        {
            title: 'Blocked Properties',
            value: blockedProperties,
            icon: XCircle,
            color: 'text-red-600',
        },
    ];

    return (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat, index) => (
                <Card key={index}>
                    <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                            <CardTitle className="text-sm font-medium text-gray-600">{stat.title}</CardTitle>
                            <stat.icon className={`h-6 w-6 ${stat.color}`} />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center space-x-2">
                            <div className="text-2xl font-bold">{stat.value}</div>
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
};

export default PropertyHeader;
