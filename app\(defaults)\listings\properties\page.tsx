'use client';

import React from 'react';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import { PropertiesManagement } from '@/components/pages/properties';


const PropertiesPage = () => {
    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Property Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Property Management' }]}
            />

            {/* Property Management Header */}
            <div className="px-4 mb-6 pt-6">

                    <div className="flex items-center gap-3 mb-2">

                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Property Management</h1>
                            <p className="text-gray-600 mt-1">Manage all properties listed by agents and agencies</p>
                        </div>
                    </div>

            </div>

            <div className="px-4">
                <PropertiesManagement />
            </div>
        </DefaultPageLayout>
    );
};

export default PropertiesPage;
