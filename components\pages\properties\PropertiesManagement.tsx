import React, { useState, useEffect } from 'react';
import { Property, PropertyStats, PropertiesResponse, PropertyPagination } from '@/utils/types/property';
import PropertyService from '@/lib/propertyService';
import PropertyHeader from './PropertyHeader';
import PropertyFilters from './PropertyFilters';
import PropertyTable from './PropertyTable';
import { useToast } from '@/components/reusable/Notify';
import { showMessage } from '@/app/lib/Alert';

const PropertiesManagement: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PropertyPagination | null>(null);
  const [loading, setLoading] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('All Types');
  const [statusFilter, setStatusFilter] = useState('All Status');
  const [locationFilter, setLocationFilter] = useState('All Emirates');
  const [listingTypeFilter, setListingTypeFilter] = useState('All Types');

  // Sorting states
  const [sortBy, setSortBy] = useState('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Mock data for properties
  const mockProperties: Property[] = [
    {
      id: 1,
      title: 'Modern 3BR Apartment in Downtown',
      type: 'residential',
      location_name: 'Downtown, Dubai',
      price: '1200000',
      status: 'Available',
      agentName: 'John Smith',
      agencyName: 'Elite Properties',
      expiryDate: '2024-12-31',
      images: [{ id: 1, url: '/default.png' }],
      featured: true,
      verified: true,
      bedrooms: 3,
      bathrooms: 2,
      area: '1200 sq ft',
      listing_type: 1, // 1 for sale
      emirate: 'dubai'
    },
    {
      id: 2,
      title: 'Luxury Villa with Pool',
      type: 'residential',
      location_name: 'Palm Jumeirah, Dubai',
      price: '2500000',
      status: 'Sold',
      agentName: 'Sarah Johnson',
      agencyName: 'Premium Homes',
      expiryDate: '2024-11-30',
      images: [{ id: 2, url: '/default.png' }],
      featured: false,
      verified: true,
      bedrooms: 5,
      bathrooms: 4,
      area: '3500 sq ft',
      listing_type: 1, // 1 for sale
      emirate: 'dubai'
    },
    {
      id: 3,
      title: 'Commercial Office Space',
      type: 'commercial',
      location_name: 'Business Bay, Dubai',
      price: '800000',
      status: 'Available',
      agentName: 'Mike Wilson',
      agencyName: 'Business Properties',
      expiryDate: '2025-01-15',
      images: [{ id: 3, url: '/default.png' }],
      featured: false,
      verified: false,
      area: '2000 sq ft',
      listing_type: 2, // 2 for rent
      emirate: 'dubai'
    }
  ];

  useEffect(() => {
    const fetchProperties = async () => {
      setLoading(true);
      try {
        const propertyService = new PropertyService();
        const response = await propertyService.getProperties();
        console.log('API Response:', response);
        if (response.success) {
          console.log('Properties received:', response.data.properties);
          setProperties(response.data.properties);
          setPagination(response.data.pagination);
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
        // For now, keep mock data as fallback
        setProperties(mockProperties);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, []);

  // Filter properties
  const filteredProperties = properties.filter(property => {
    const searchValue = searchTerm.toLowerCase();
    const matchesSearch = (property.title?.toLowerCase().includes(searchValue) ?? false) ||
                         (property.name?.toLowerCase().includes(searchValue) ?? false) ||
                         (property.location_name?.toLowerCase().includes(searchValue) ?? false) ||
                         (property.agentName?.toLowerCase().includes(searchValue) ?? false);

    const matchesType = typeFilter === 'All Types' || property.type === typeFilter;
    const matchesStatus = statusFilter === 'All Status' || property.status === statusFilter;
    const matchesLocation = locationFilter === 'All Emirates' || property.emirate === locationFilter;
    const matchesListingType = listingTypeFilter === 'All Types' ||
                         property.listing_type?.toString() === listingTypeFilter;

    return matchesSearch && matchesType && matchesStatus && matchesLocation && matchesListingType;
  });

  // Sort properties
  const sortedProperties = [...filteredProperties].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    // Handle field name mapping
    if (sortBy === 'title') {
      aValue = a.name || a.title || '';
      bValue = b.name || b.title || '';
    } else if (sortBy === 'expiryDate') {
      aValue = a.expiryDate || a.expiry_date || '';
      bValue = b.expiryDate || b.expiry_date || '';
    } else {
      aValue = a[sortBy as keyof Property];
      bValue = b[sortBy as keyof Property];
    }

    if (sortBy === 'price') {
      aValue = Number(aValue);
      bValue = Number(bValue);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Calculate stats
  const stats: PropertyStats = {
    totalProperties: properties.length,
    activeProperties: properties.filter(p => p.status === 'Available').length,
    pendingProperties: properties.filter(p => p.status === 'Unpublished').length,
    blockedProperties: properties.filter(p => p.status === 'Blocked').length
  };

  // Check if filters are applied
  const hasFilters = searchTerm !== '' || typeFilter !== 'all' || statusFilter !== 'all' ||
                    locationFilter !== 'all' || listingTypeFilter !== 'all';

  // Event handlers
  const handleSelectProperty = (propertyId: number) => {
    const idStr = propertyId.toString();
    setSelectedProperties(prev =>
      prev.includes(idStr)
        ? prev.filter(id => id !== idStr)
        : [...prev, idStr]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedProperties(checked ? sortedProperties.map(p => p.id.toString()) : []);
  };

  const handleDeleteProperty = async (propertyId: number) => {
    try {
      const propertyService = new PropertyService();
      const response = await propertyService.deleteProperty(propertyId);

      if (response.success) {
        // Remove from local state
        const idStr = propertyId.toString();
        setProperties(prev => prev.filter(p => p.id !== propertyId));
        setSelectedProperties(prev => prev.filter(id => id !== idStr));
        // You can add toast notification here if needed
      } else {
        console.error('Failed to delete property:', response.message);
        // You can show error toast here
      }
    } catch (error) {
      console.error('Error deleting property:', error);
      // You can show error toast here
    }
  };

  const handleBlockProperty = (propertyId: number) => {
    setProperties(prev => prev.map(p =>
      p.id === propertyId ? { ...p, status: 'Blocked' } : p
    ));
  };

  const handleStatusChange = async (propertyId: number, status: Property['status'], reason?: string) => {
    try {
      // Convert frontend status to backend format (lowercase)
      const backendStatus = status?.toLowerCase();

      // Debug logging
      console.log('handleStatusChange called with:', {
        propertyId,
        status,
        backendStatus,
        reason,
        reasonTrimmed: reason?.trim()
      });

      // Validate that reason is provided for blocking
      if (backendStatus === 'blocked' && !reason?.trim()) {
        showMessage('A reason is required when blocking a property.', 'error');
        return;
      }

      // Call the API to update status on backend
      const { updatePropertyStatus } = await import('@/lib/propertyService');
      const result = await updatePropertyStatus(propertyId.toString(), backendStatus || '', reason);

      if (result.success) {
        // Update local state only if API call succeeds
        setProperties(prev => prev.map(p =>
          p.id === propertyId ? { ...p, status, status_name: status } : p
        ));

        // Show success notification
        const reasonText = reason ? ` with reason: "${reason}"` : '';
        showMessage(`Property status updated to ${status} successfully${reasonText}.`, 'success');
      } else {
        // Show error notification
        showMessage('Failed to update property status. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error updating property status:', error);
      showMessage('An error occurred while updating property status.', 'error');
    }
  };

  const handleAddNote = async (propertyId: number, note: string) => {
    try {
      const propertyService = new PropertyService();
      const result = await propertyService.addNote(propertyId, note);

      if (result.success) {
        showMessage('Note added successfully to the property.', 'success');
      } else {
        showMessage('Failed to add note. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error adding note to property:', error);
      showMessage('An error occurred while adding the note.', 'error');
    }
  };

  const handleBulkDelete = () => {
    const selectedIds = selectedProperties.map(id => parseInt(id));
    setProperties(prev => prev.filter(p => !selectedIds.includes(p.id)));
    setSelectedProperties([]);
  };

  const handleBulkBlock = () => {
    const selectedIds = selectedProperties.map(id => parseInt(id));
    setProperties(prev => prev.map(p =>
      selectedIds.includes(p.id) ? { ...p, status: 'Blocked' } : p
    ));
    setSelectedProperties([]);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setTypeFilter('All Types');
    setStatusFilter('All Status');
    setLocationFilter('All Emirates');
    setListingTypeFilter('All Types');
  };

  return (
    <div className="space-y-6">
      <PropertyHeader
        totalProperties={stats.totalProperties}
        activeProperties={stats.activeProperties}
        pendingProperties={stats.pendingProperties}
        blockedProperties={stats.blockedProperties}
      />

      <PropertyFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        typeFilter={typeFilter}
        setTypeFilter={setTypeFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        locationFilter={locationFilter}
        setLocationFilter={setLocationFilter}
        listingTypeFilter={listingTypeFilter}
        setListingTypeFilter={setListingTypeFilter}
        filteredCount={filteredProperties.length}
        totalCount={properties.length}
        hasFilters={hasFilters}
        onClearFilters={handleClearFilters}
      />

      <PropertyTable
        properties={sortedProperties}
        selectedProperties={selectedProperties}
        onSelectProperty={handleSelectProperty}
        onSelectAll={handleSelectAll}
        onDeleteProperty={handleDeleteProperty}
        onBlockProperty={handleBlockProperty}
        onStatusChange={handleStatusChange}
        onAddNote={handleAddNote}
        onBulkDelete={handleBulkDelete}
        onBulkBlock={handleBulkBlock}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSort={handleSort}
      />
    </div>
  );
};

export default PropertiesManagement;
