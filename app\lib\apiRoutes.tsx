export const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.findanyagent.ae';
export const URL = BASE_URL + '/api/v1';

// Subscription Management Endpoints
export const PACKAGES_API = URL + '/admin/subscription/packages';
export const FEATURES_API = URL + '/admin/subscription/features';
export const FEATURE_VALUES_API = URL + '/admin/subscription/feature-values';
// export const PACKAGES_WITH_FEATURES_API = URL + '/admin/subscription/packages-with-features';
// export const PACKAGES_BY_USER_TYPE_API = URL + '/admin/subscription/packages/user-type';
export const FEATURE_VALUES_BY_PACKAGE_USER_TYPE_API = URL + '/admin/subscription/feature-values/package';
export const STATUS_API = URL + '/other/status';
export const DISCOUNTS_API = URL + '/admin/discounts/all';

// User Management Endpoints
export const USERS_API = URL + '/admin/user-management/users';
export const USERS_DOWNLOAD_CSV_API = URL + '/admin/user-management/users/download-csv';
export const USERS_ADD_USER_API = URL + '/admin/user-management/users/add-user';
export const USERS_BULK_EMAIL_API = URL + '/admin/user-management/users/bulk-email';
export const USERS_INDIVIDUAL_EMAIL_API = URL + '/admin/user-management/users/individual-email';
export const USERS_DEACTIVATE_API = URL + '/admin/user-management/users/:id/deactivate';
export const USERS_ACTIVATE_API = URL + '/admin/user-management/users/:id/activate';
export const USERS_RESET_PASSWORD_API = URL + '/admin/user-management/users/:id/reset-password';
export const USERS_ADD_NOTE_API = URL + '/admin/user-management/users/:id/notes';
export const USERS_GET_NOTES_API = URL + '/admin/user-management/users/:id/notes';

// Support Tickets Endpoints
export const TICKETS_API = URL + '/admin/tickets';
export const TICKETS_SUMMARY_API = URL + '/admin/tickets/summary';
export const TICKETS_META_API = URL + '/admin/tickets/meta';
export const TICKET_RESPONSES_API = URL + '/admin/tickets/:id/responses';

// Review Management Endpoints
export const REVIEWS_API = URL + '/admin/reviews';
export const REVIEWS_STATS_API = URL + '/admin/reviews/stats';
export const REVIEW_UPDATE_STATUS_API = (id: number) => URL + `/admin/reviews/${id}/status`;
export const REVIEW_DELETE_API = (id: number) => URL + `/admin/reviews/${id}`;
export const REVIEW_ADD_NOTE_API = (id: number) => URL + `/admin/reviews/${id}/notes`;
export const REVIEW_HIDDEN_API = (id: number) => URL + `/admin/reviews/${id}/hide`;
export const REVIEW_RESTORE_API = (id: number) => URL + `/admin/reviews/${id}/restore`;
export const REVIEW_FLAG_API = (id: number) => URL + `/admin/reviews/${id}/flag`;

// Property Management Endpoints
export const PROPERTIES_API = URL + '/admin/properties';
export const PROPERTY_DELETE_API = (id: number) => URL + `/admin/properties/${id}`;
// Agent Management Endpoints
export const AGENT_SEARCH_API = URL + '/admin/agents/search';

type API_ENDPOINTS = {
    LOGIN: string;
    FORGET_PASSWORD: string;
    REGISTER: string;
    USER_PROFILE_DATA: string;
    GET_USER_PROFILE: string;
    RESET_PASSWORD: string;
    VERIFY_TOKEN: string;
    CHANGE_PASSWORD: string;
    CHANGE_SETTING: string;
    GET_USER_SETTING: string;
    UPDATE_PASSWORD: string;
    [key: string]: string;
};

const API_ENDPOINTS = {
    LOGIN: URL + '/admin/login',
    GET_SESSION: URL + '/admin/session/',
    LOGOUT: URL + '/auth/logout',
    AGENT_APPLICATIONS: URL + '/admin/agents',
    DOCUMENTS: URL + '/admin/manage-documents',
    ALL_LOCATIONS: URL + '/location',
    APPLICATION_COUNT: URL + '/admin/dashboard',
    LEADS: URL + '/admin/leads',
    GET_VERIFIED_AGENTS: URL + '/admin/agents/all/verified',
    CONVERT_LEADS: URL + '/admin/leads/convert-leads',
    GET_NOTES: URL + '/admin/leads/note/',
    LEAD_BULK: URL + '/admin/leads/bulk',
    SALESPERSONS: URL + '/admin/salespersons',
    REFERRALS: URL + '/admin/referrals',
    PAYMENTS: URL + '/admin/referrals/payments',
    SALESPERSONS_COMMISSIONS: URL + '/admin/salespersons/commissions',

    UPLOAD_FILE: URL + '/other/documents',
    BLOG_CREATE: URL + '/admin/blogs/create',
    BLOG_UPDATE: URL + '/admin/blogs/update/',
    BLOG_ALL: URL + '/admin/blogs/all',
    BLOG_PUBLISHED: URL + '/admin/blogs/published',
    BLOG_DELETED: URL + '/admin/blogs/delete/',
    BLOG_LIKE: URL + '/admin/blogs/like/',
    BLOG_UPDATE_STATUS: URL + '/admin/blogs/status/',
    BLOG_GET_ONE: URL + '/admin/blogs/get/',
    GET_ALL_NEWSLETTER: URL + '/admin/newsletter/all',
    GET_ALL_NEWSLETTER_COUNT: URL + '/admin/newsletter/count',
    UPDATE_NEWSLETTER: URL + '/admin/newsletter/update',
    DLETE_NEWSLETTER: URL + '/admin/newsletter/delete/',

    GET_RECENT_REFERRALS: URL + '/admin/referrals/recent',
    GET_TOP_REFERRALS: URL + '/admin/referrals/top',
    GET_DASHBOARD_REFERRALS: URL + '/admin/referrals/dashboard',

    SEND_BULK_NEWSLETTER_EMAIL: URL + '/admin/newsletter/send',
    SALESPERSON_STATUS: (id: number) => URL +  `/admin/salespersons/${id}/status`,
    SETTINGS: URL + '/admin/settings',
    DISCOUNTS: URL + '/admin/discounts',
    SERVICES_URL: URL + '/admin/services',
};

export default API_ENDPOINTS;
