'use client';

import React, { useEffect, useMemo, useState, useRef, useCallback } from 'react';
import Image from 'next/image';
import Select from 'react-select';
import SearchInput from '@/components/reusable/SearchBar';
import Loading from '@/components/layouts/loading';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { PaginationRightIcon, PaginationLeftIcon, PaginationDownIcon } from '@/components/icon/Icon';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import { getServices, getServiceFilters, deleteServiceData, saveServiceNotes, changeServiceStatus } from '.';
import { Service, ServicesResponse, ServiceFilterResponse } from './ServiceDTO';
import ServiceActions from './ServiceAction';
import Modal from '@/components/reusable/modals/modal';
import DeleteServiceModal from './DeleteServiceModal';
import ViewServiceModal from './ViewServiceModal';
import AddNoteModal from './AddNoteModal';
import StatusUpdateServiceModal from './StatusUpdateServiceModal';

const statusStyles = {
    Active: 'bg-green-100 text-green-600', // Positive, ongoing
    Draft: 'bg-gray-100 text-gray-600', // Neutral, work-in-progress
    Publish: 'bg-blue-100 text-blue-600', // Primary action / visibility
    Unpublished: 'bg-yellow-100 text-yellow-700', // Pending, attention needed
    Blocked: 'bg-red-100 text-red-600', // Error, restricted
};

// Money + date formatters
const formatMoney = (n: number | string) => new Intl.NumberFormat('en-AE', { style: 'currency', currency: 'AED' }).format(Number(n ?? 0));

const formatDate = (iso: string) => new Date(iso).toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' }).split('/').reverse().join('-');

export default function ServicesManagement() {
    // Table state
    const [rows, setRows] = useState<Service[]>([]);
    const [tableLoading, setTableLoading] = useState(false);
    const [selectedRecordId, setSelectedRecordId] = useState<number>();
    const [warningModal, setWarningModal] = useState(false);
    const [viewModal, setViewModal] = useState(false);
    const [addNoteModal, setAddNoteModal] = useState(false);
    const [hideServiceModal, setHideServiceModal] = useState(false);
    // Pagination
    const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
    const totalPages = Math.max(1, Math.ceil(pagination.total / pagination.pageSize));
    const pageSizeBtnRef = useRef<HTMLButtonElement>(null);
    const [showDropdown, setShowDropdown] = useState(false);
    const [dropUp, setDropUp] = useState(false);

    // Filters
    const [search, setSearch] = useState('');
    const [debounced, setDebounced] = useState('');
    const [selectedStatus, setSelectedStatus] = useState<any>(null);
    const [selectedType, setSelectedType] = useState<any>(null);
    const [selectedLocation, setSelectedLocation] = useState<any>(null);

    // Filter options
    const [statusOptions, setStatusOptions] = useState<{ label: string; value: string | number }[]>([]);
    const [typeOptions, setTypeOptions] = useState<{ label: string; value: string | number }[]>([]);
    const [locationOptions, setLocationOptions] = useState<{ label: string; value: string | number }[]>([]);

    // KPI counts
    const [counts, setCounts] = useState<{ total_services: number; services_by_status: { status: string; count: number; statusId: number }[] }>({
        total_services: 0,
        services_by_status: [],
    });

    // Debounce search
    useEffect(() => {
        const t = setTimeout(() => setDebounced(search.trim()), 300);
        return () => clearTimeout(t);
    }, [search]);

    // Load filter options
    useEffect(() => {
        const loadFilters = async () => {
            try {
                const resp = await getServiceFilters(`${API_ENDPOINTS.SERVICES_URL}/get/status-locations-and-types`);
                if (!resp.success) return;
                const { status, durations, locations } = resp.data as ServiceFilterResponse;

                setStatusOptions([{ label: 'All Status', value: '' }, ...status.map((s) => ({ label: s.name, value: s.id }))]);

                const allDurations =
                    durations.flatMap((d) =>
                        d.children.map((c) => ({
                            label: c?.name?.charAt(0)?.toUpperCase() + c?.name?.slice(1),
                            value: c.id,
                        }))
                    ) ?? [];
                setTypeOptions([{ label: 'All Types', value: '' }, ...allDurations]);

                setLocationOptions([
                    { label: 'All Pricing', value: '' },
                    { label: 'Free', value: 'free' },
                    { label: 'Paid', value: 'paid' },
                ]);
            } catch (err) {
                console.error(err);
                showMessage('Failed to load filters', 'error');
            }
        };
        loadFilters();
    }, []);

    // Build API URL
    const buildUrl = useCallback(
        (pg: number, sz: number) => {
            const params = new URLSearchParams();
            params.set('page', String(pg));
            params.set('pageSize', String(sz));
            if (debounced) params.set('search', debounced);
            if (selectedStatus) params.set('statusId', String(selectedStatus));
            if (selectedType) params.set('typeId', String(selectedType));
            if (selectedLocation) params.set('pricing', String(selectedLocation));

            return `${API_ENDPOINTS.SERVICES_URL}?${params.toString()}`;
        },
        [debounced, selectedStatus, selectedType, selectedLocation]
    );

    // Fetch services
    const loadPage = useCallback(
        async (pg = pagination.page, sz = pagination.pageSize) => {
            try {
                setTableLoading(true);
                const url = buildUrl(pg, sz);
                const payload = await getServices(url);
                if (!payload.success) return;

                const data: ServicesResponse = payload.data;
                setRows(data.items);
                setCounts(data.counts);
                setPagination({ page: data.pagination.page, pageSize: data.pagination.limit, total: data.pagination.total });
            } catch (err) {
                console.error(err);
                showMessage('Failed to load services', 'error');
            } finally {
                setTableLoading(false);
            }
        },
        [buildUrl, pagination.page, pagination.pageSize]
    );

    // Reload when page changes
    useEffect(() => {
        loadPage(pagination.page, pagination.pageSize);
    }, [pagination.page, pagination.pageSize, loadPage]);

    // Reset to page 1 when filters change
    useEffect(() => {
        setPagination((prev) => ({ ...prev, page: 1 }));
    }, [debounced, selectedStatus, selectedType, selectedLocation]);

    // Pagination helpers
    const setPageSafe = (p: number) => {
        const next = Math.min(Math.max(1, p), totalPages);
        if (next !== pagination.page) setPagination((prev) => ({ ...prev, page: next }));
    };

    const getPaginationRange = (currentIndexZeroBased: number, total: number) => {
        const MAX = 5;
        let start = Math.max(0, currentIndexZeroBased - 2);
        let end = Math.min(total, start + MAX);
        if (end - start < MAX) start = Math.max(0, end - MAX);
        return Array.from({ length: end - start }, (_, i) => start + i);
    };

    const togglePageSizeDropdown = () => {
        setShowDropdown((prev) => {
            const next = !prev;
            if (next && pageSizeBtnRef.current) {
                const rect = pageSizeBtnRef.current.getBoundingClientRect();
                const spaceBelow = window.innerHeight - rect.bottom;
                setDropUp(spaceBelow < 160);
            }
            return next;
        });
    };

    const filteredRows = useMemo(() => rows, [rows]);

    const handleDeleteService = (id: number) => {
        setSelectedRecordId(id);
        setWarningModal(true);
    };

    const handleViewService = (service: any) => {
        setSelectedRecordId(service.id);
        setViewModal(true);
    };

    const deleteService = async (id: number) => {
        try {
            setTableLoading(true);
            const response = await deleteServiceData(`${API_ENDPOINTS.SERVICES_URL}/${id}`);

            if (response.success) {
                showMessage('Service deleted successfully.', 'success');
                loadPage();
            } else {
                showMessage(response.message || 'Failed to delete service.', 'error');
            }
        } catch (error) {
            console.error('Delete service error:', error);
            showMessage('Failed to delete service.', 'error');
        } finally {
            setTableLoading(false);
        }
    };

    const handleAddNote = (service: any) => {
        setSelectedRecordId(service.id);
        setAddNoteModal(true);
    };

    const addNoteToService = async (note: string) => {
        if (selectedRecordId === undefined) {
            console.error('Selected record ID is undefined.');
            return;
        }

        try {
            setTableLoading(true);
            const response = await saveServiceNotes(`${API_ENDPOINTS.SERVICES_URL}/${selectedRecordId}/notes`, note);

            if (response.success) {
                showMessage('Note added successfully.', 'success');
                setAddNoteModal(false);
                loadPage();
            } else {
                showMessage(response.message || 'Failed to add note.', 'error');
            }
        } catch (error) {
            console.error('Add note error:', error);
            showMessage('Failed to add note.', 'error');
        } finally {
            setTableLoading(false);
        }
    };

    const handleHideService = (service: any) => {
        if (!service.id) {
            console.error('Service ID is missing.');
            return;
        }
        setSelectedRecordId(service.id);
        setHideServiceModal(true);
    };

    const hideService = async (id: number, reason: string) => {
        try {
            const service = filteredRows.find((s) => s.id === id);
            if (!service) {
                showMessage('Service not found.', 'error');
                return;
            }

            setTableLoading(true);
            let targetStatus;
            let targetStatusId;

            // If service is not already blocked, set it to blocked
            if (service.statusname.toLowerCase() != 'blocked') {
                targetStatus = statusOptions.find((opt: any) => opt.label.toLowerCase() == 'blocked');
                targetStatusId = targetStatus?.value;
            } else {
                // If service is already blocked, set it to activated
                targetStatus = statusOptions.find((opt: any) => opt.label.toLowerCase() == 'publish');
                targetStatusId = targetStatus?.value;
            }

            if (!targetStatus && !targetStatusId) {
                showMessage('No alternative status available.', 'error');
                return;
            }

            const response = await changeServiceStatus(`${API_ENDPOINTS.SERVICES_URL}/${service.id ?? id}/status`, String(targetStatusId), reason);

            if (response.success) {
                showMessage(`Service Marked As ${targetStatus?.label} Successfully.`, 'success');
                setHideServiceModal(false);
                loadPage();
            } else {
                showMessage(response.message || 'Failed to change service status.', 'error');
            }
        } catch (error) {
            console.error('Change service status error:', error);
            showMessage('Failed to change service status.', 'error');
        } finally {
            setTableLoading(false);
        }
    };

    return (
        <DefaultPageLayout>
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-semibold">Service Management</h1>
                <p className="text-sm text-gray-500">Manage all services offered by agents and agencies</p>
            </div>

            {/* KPI cards */}
            <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 xl:grid-cols-5">
                <div
                    className={`cursor-pointer rounded-2xl border-2 p-5 shadow-sm transition ${!selectedStatus ? 'border-blue-500 bg-blue-50' : 'border-[#e4e4e4] bg-white'}`}
                    onClick={() => {
                        setPageSafe(1);
                        setSelectedStatus(null);
                    }}
                >
                    <p className="text-sm text-gray-500">Total Services</p>
                    <p className="mt-1 text-2xl font-semibold text-gray-900">{counts.total_services}</p>
                </div>
                {counts.services_by_status?.map((s) => (
                    <div
                        key={s.status}
                        className={`cursor-pointer rounded-2xl border-2 p-5 shadow-sm transition ${selectedStatus === s.statusId ? 'border-blue-500 bg-blue-50' : 'border-[#e4e4e4] bg-white'}`}
                        onClick={() => {
                            setPageSafe(1);
                            setSelectedStatus(s.statusId);
                        }}
                    >
                        <p className="text-sm text-gray-500">{s.status} Services</p>
                        <p className="mt-1 text-2xl font-semibold text-gray-900">{s.count}</p>
                    </div>
                )) || []}
            </div>

            {/* Filters */}
            <div className="mb-6 rounded-lg border bg-white p-4">
                <div className="mb-4 flex items-center gap-2">
                    <span className="font-medium text-gray-700">Filters</span>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                    {/* Search */}
                    <div className="md:col-span-2">
                        <SearchInput value={search} onChange={(e: any) => setSearch(e.target.value)} placeholder="Search services, agents, or agencies..." />
                    </div>

                    {/* React Selects */}
                    <Select
                        className="react-select-container"
                        classNamePrefix="react-select"
                        options={typeOptions}
                        value={typeOptions.find((opt) => opt.value === selectedType) || null}
                        onChange={(opt: any) => setSelectedType(opt.value)}
                        isSearchable
                        placeholder="All Types"
                        styles={{
                            control: (base: any) => ({
                                ...base,
                                minHeight: '56px', // match h-14
                                height: '56px',
                                borderRadius: '0.5rem',
                                borderColor: '#E4E4E4',
                                boxShadow: 'none',
                                '&:hover': { borderColor: '#1D7EB6' },
                            }),
                            valueContainer: (base: any) => ({
                                ...base,
                                height: '56px',
                                padding: '0 12px',
                            }),
                            indicatorsContainer: (base: any) => ({
                                ...base,
                                height: '56px',
                            }),
                        }}
                    />

                    <Select
                        className="react-select-container"
                        classNamePrefix="react-select"
                        options={statusOptions}
                        value={statusOptions.find((opt) => opt.value === selectedStatus) || null}
                        onChange={(opt: any) => setSelectedStatus(opt.value)}
                        isSearchable
                        placeholder="All Status"
                        styles={{
                            control: (base: any) => ({
                                ...base,
                                minHeight: '56px',
                                height: '56px',
                                borderRadius: '0.5rem',
                                borderColor: '#E4E4E4',
                                boxShadow: 'none',
                                '&:hover': { borderColor: '#1D7EB6' },
                            }),
                            valueContainer: (base: any) => ({
                                ...base,
                                height: '56px',
                                padding: '0 12px',
                            }),
                            indicatorsContainer: (base: any) => ({
                                ...base,
                                height: '56px',
                            }),
                        }}
                    />

                    <Select
                        className="react-select-container"
                        classNamePrefix="react-select"
                        options={locationOptions}
                        value={locationOptions.find((opt) => opt.value === selectedLocation) || null}
                        onChange={(opt: any) => setSelectedLocation(opt.value)}
                        isSearchable
                        placeholder="All Pricing"
                        styles={{
                            control: (base: any) => ({
                                ...base,
                                minHeight: '56px',
                                height: '56px',
                                borderRadius: '0.5rem',
                                borderColor: '#E4E4E4',
                                boxShadow: 'none',
                                '&:hover': { borderColor: '#1D7EB6' },
                            }),
                            valueContainer: (base: any) => ({
                                ...base,
                                height: '56px',
                                padding: '0 12px',
                            }),
                            indicatorsContainer: (base: any) => ({
                                ...base,
                                height: '56px',
                            }),
                        }}
                    />
                </div>
            </div>

            {/* Table */}
            <div className="relative min-h-[200px] rounded-lg border bg-white">
                <table className="w-full text-left">
                    <thead className="sticky top-0 bg-[#f8f9fa] text-[#636363]">
                        <tr>
                            <th className="px-6 py-4">Service</th>
                            <th className="px-6 py-4">Type</th>
                            <th className="px-6 py-4">Location</th>
                            <th className="px-6 py-4">Pricing</th>
                            <th className="px-6 py-4">Views</th>
                            <th className="px-6 py-4">Agency/Agent</th>
                            <th className="px-6 py-4">Duration</th>
                            <th className="px-6 py-4">Status</th>
                            <th className="px-6 py-4">Actions</th>
                        </tr>
                    </thead>

                    <tbody className={`overflow-auto  ${tableLoading ? 'pointer-events-none opacity-50' : ''}`}>
                        {filteredRows.map((s) => (
                            <tr key={s.id} className="border-b hover:bg-gray-50">
                                {/* Service */}
                                <td className="flex items-center gap-2 px-6 py-4">
                                    {s.images?.length > 0 ? (
                                        <Image src={`${process.env.NEXT_PUBLIC_DOCUMENTS_BASE_URL}${s.images[0].url}`} alt={s.title} width={40} height={40} className="rounded-full object-cover" />
                                    ) : (
                                        <Image
                                            src={`https://ui-avatars.com/api/?name=${encodeURIComponent(s.title.substring(0, 2))}&background=random&color=fff&size=40`}
                                            alt={s.title}
                                            width={40}
                                            height={40}
                                            className="rounded-full object-cover"
                                        />
                                    )}
                                    <div>
                                        <div className="font-semibold text-[#2d2d2e]">{s.title}</div>
                                        <div className="text-xs text-gray-500">ID: srv-{s.id}</div>
                                    </div>
                                </td>

                                <td className="px-6 py-4">{s.services[0]?.label ?? '-'}</td>
                                <td className="px-6 py-4">{s.locations[0]?.label ?? '-'}</td>
                                <td className="px-6 py-4">{s.isFree ? 'Free' : formatMoney(s.price)}</td>
                                <td className="px-6 py-4 text-sm text-gray-700">{s.views ?? 0} views</td>

                                {/* Agent */}
                                <td className="px-6 py-4">
                                    <div className="flex items-center gap-2">
                                        {s.profile?.profileImage ? (
                                            <Image src={s.profile.profileImage} alt={s.profile.firstName} width={40} height={40} className="rounded-full object-cover" />
                                        ) : (
                                            <Image
                                                src={`https://ui-avatars.com/api/?name=${encodeURIComponent((s.profile.firstName || 'U').substring(0, 2))}&background=random&color=fff&size=40`}
                                                alt={s.profile.firstName || 'User'}
                                                width={40}
                                                height={40}
                                                className="rounded-full object-cover"
                                            />
                                        )}
                                        <div>
                                            <p className="font-medium">
                                                {s.profile.firstName} {s.profile.lastName}
                                            </p>
                                            <p className="text-xs text-gray-500">{s.profile.email}</p>
                                        </div>
                                    </div>
                                </td>

                                <td className="px-6 py-4">{s?.durationtype?.name?.toUpperCase() || 'Not available'}</td>

                                {/* Status pill */}
                                <td className="px-6 py-4">
                                    <div className="flex flex-col items-center space-y-1">
                                        {s.statusname ? (
                                            <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${statusStyles[s.statusname as keyof typeof statusStyles] ?? ''}`}>
                                                {s.statusname}
                                            </span>
                                        ) : (
                                            <span className="inline-flex items-center rounded-md px-2 py-1 text-xs text-gray-500">—</span>
                                        )}
                                    </div>
                                </td>

                                {/* Actions */}
                                <ServiceActions
                                    service={s}
                                    onView={(srv: any) => handleViewService(srv)}
                                    // onEdit={(srv: any) => console.log('Edit', srv)}
                                    onAddNote={(srv: any) => handleAddNote(srv)}
                                    onBlock={(srv: any) => {
                                        handleHideService(srv);
                                        // if (srv.statusname.toLowerCase() != 'blocked') {
                                        //     console.log(srv.statusname, '!= blocked');
                                        //     handleHideService(srv);
                                        // } else {
                                        //     console.log(srv.statusname, '== blocked');

                                        //     hideService(srv.id, 'No reason provided');
                                        // }
                                    }}
                                    // onBlock={(srv: any) => hideService(srv.id, 'No reason provided')}
                                    onDelete={(id: string) => handleDeleteService(Number(id))}
                                />
                            </tr>
                        ))}

                        {filteredRows.length === 0 && !tableLoading && (
                            <tr>
                                <td className="px-6 py-10 text-center text-sm text-gray-500" colSpan={8}>
                                    No services found.
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>

                {/* Loader overlay */}
                {tableLoading && (
                    <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/60">
                        <Loading fullscreen={false} />
                    </div>
                )}
            </div>

            {/* Pagination */}
            <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                <div className="flex items-center gap-2">
                    <button onClick={() => setPageSafe(pagination.page - 1)} disabled={pagination.page === 1} className="rounded-md p-2 disabled:opacity-50">
                        <PaginationRightIcon />
                    </button>
                    <div className="flex space-x-1">
                        {getPaginationRange(pagination.page - 1, totalPages).map((i) => (
                            <button key={i} onClick={() => setPageSafe(i + 1)} className={`rounded-md px-3 py-1 ${pagination.page === i + 1 ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}>
                                {i + 1}
                            </button>
                        ))}
                    </div>
                    <button onClick={() => setPageSafe(pagination.page + 1)} disabled={pagination.page === totalPages} className="rounded-md p-2 disabled:opacity-50">
                        <PaginationLeftIcon />
                    </button>
                </div>
                <div className="relative flex items-center gap-2">
                    <span className="text-sm text-gray-500">Showing</span>
                    <div className="relative">
                        <button ref={pageSizeBtnRef} onClick={togglePageSizeDropdown} className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm">
                            {pagination.pageSize}
                            <PaginationDownIcon />
                        </button>
                        {showDropdown && (
                            <div className={`absolute left-0 z-10 w-16 rounded-md border border-gray-200 bg-white shadow-lg ${dropUp ? 'bottom-full mb-2' : 'top-full mt-2'}`}>
                                <div className="py-1">
                                    {[10, 20, 50].map((value) => (
                                        <button
                                            key={value}
                                            className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                            onClick={() => {
                                                setPagination((prev) => ({ ...prev, page: 1, pageSize: value }));
                                                setShowDropdown(false);
                                            }}
                                        >
                                            {value}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                    <span className="text-sm text-gray-500">Services out of {pagination.total}</span>
                </div>
            </div>

            {/* Modals */}
            <Modal isOpen={warningModal} onClose={() => setWarningModal(false)}>
                <DeleteServiceModal
                    service={filteredRows.find((s) => s.id === selectedRecordId)}
                    onClose={() => setWarningModal(false)}
                    onConfirm={() => {
                        if (selectedRecordId) {
                            deleteService(selectedRecordId);
                            setWarningModal(false);
                        }
                    }}
                />
            </Modal>

            {/* View Modals */}
            <Modal isOpen={viewModal} onClose={() => setViewModal(false)}>
                <ViewServiceModal serviceId={selectedRecordId!} onClose={() => setViewModal(false)} />
            </Modal>

            <Modal isOpen={addNoteModal} onClose={() => setAddNoteModal(false)}>
                <AddNoteModal
                    service={filteredRows.find((s) => s.id === selectedRecordId)!}
                    onClose={() => {
                        setAddNoteModal(false);
                        setSelectedRecordId(undefined);
                    }}
                    onConfirm={addNoteToService}
                />
            </Modal>

            <Modal isOpen={hideServiceModal} onClose={() => setHideServiceModal(false)}>
                <StatusUpdateServiceModal
                    service={filteredRows.find((s) => s.id === selectedRecordId)}
                    onClose={() => {
                        setHideServiceModal(false);
                        setSelectedRecordId(undefined);
                    }}
                    onConfirm={(reason: string) => {
                        if (selectedRecordId !== undefined) {
                            hideService(selectedRecordId, reason);
                        } else {
                            console.error('Selected record ID is undefined.');
                        }
                    }}
                />
            </Modal>
        </DefaultPageLayout>
    );
}
