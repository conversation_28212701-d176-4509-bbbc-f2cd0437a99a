import * as React from "react"
import { Check, Minus } from "lucide-react"

export interface CheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  indeterminate?: boolean
  onCheckedChange?: (checked: boolean) => void
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, indeterminate, onCheckedChange, ...props }, ref) => {
    const inputRef = React.useRef<HTMLInputElement>(null)

    React.useImperativeHandle(ref, () => inputRef.current!)

    React.useEffect(() => {
      if (inputRef.current) {
        inputRef.current.indeterminate = !!indeterminate
      }
    }, [indeterminate])

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      onCheckedChange?.(event.target.checked)
      props.onChange?.(event)
    }

    return (
      <div className="relative inline-flex items-center">
        <input
          type="checkbox"
          ref={inputRef}
          className={`
            peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 
            ring-offset-white focus-visible:outline-none focus-visible:ring-2 
            focus-visible:ring-blue-500 focus-visible:ring-offset-2 
            disabled:cursor-not-allowed disabled:opacity-50 
            data-[state=checked]:bg-blue-600 data-[state=checked]:text-white
            ${className || ''}
          `}
          onChange={handleChange}
          {...props}
        />
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center text-white">
          {indeterminate ? (
            <Minus className="h-3 w-3" />
          ) : props.checked ? (
            <Check className="h-3 w-3" />
          ) : null}
        </div>
      </div>
    )
  }
)
Checkbox.displayName = "Checkbox"

export { Checkbox }
