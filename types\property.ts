export interface Property {
  id: number;
  title: string;
  description?: string;
  type: string; // Changed from union to string for flexibility
  location: string;
  price: number;
  status: string; // Changed from union to string for API flexibility
  agentName?: string;
  agencyName?: string;
  expiryDate?: string;
  images?: string[];
  featured?: boolean;
  verified?: boolean;
  bedrooms?: number;
  bathrooms?: number;
  area?: string;
  yearBuilt?: number;
  features?: string[];
  listingType?: string;
  emirate?: string;
  createdAt?: string;
  updatedAt?: string;
  views?: number;
  notes?: PropertyNote[];
  propertyTypeId?: number;
  locationId?: number;
}

export interface PropertyNote {
  id: string;
  propertyId: string;
  note: string;
  createdBy: string;
  createdAt: string;
}

export interface PropertyFilter {
  search: string;
  status: string;
  type: string;
  location: string;
  listingType: string;
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface PropertyStats {
  totalProperties: number;
  activeProperties: number;
  pendingProperties: number;
  blockedProperties: number;
}

export interface PropertyPagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PropertiesResponse {
  status: number;
  success: boolean;
  message: string;
  data: {
    properties: Property[];
    pagination: PropertyPagination;
  };
}
