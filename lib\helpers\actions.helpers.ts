import { fetchClient } from "@/lib/fetch-client";
import { FetchClientProps } from "@/lib/fetch-client";

import { buildUrl, objectToFormData } from "@/utils/lib/utils";
import { ExecuteApiRequestProps, ExecuteDeleteApiRequestProps, ExecuteGetApiRequestProps, ExecuteMutationApiRequestProps, ExecutePatchApiRequestProps, ExecutePostApiRequestProps, ExecutePutApiRequestProps, PostBody, QueryParams, REQUEST_TYPE } from "@/utils/types/action-helper.type";
import { ApiResponse } from "@/utils/types/utility.type";


/**
 * Utility function to get the options for a mutation request,
 * mutation requests are POST, PUT, PATCH requests
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 *   - body: The request body object
 *   - method: The request method
 *   - options: Optional request options
 *   - isFormData: Whether the request body is a FormData object
 * @returns The options for the mutation request
 */
function getMutationOptions(props: ExecuteMutationApiRequestProps) {
    const body = props.isFormData
        ? objectToFormData(props.body)
        : JSON.stringify(props.body)

    const options: FetchClientProps = {
        url: props.url,
        options: {
            body,
            method: props.method,
            ...props.options,
            headers: {
                ...(props.isFormData ? {} : { "Content-Type": "application/json" }),
                ...props.options?.headers,
            }
        },
        withAuth: props.withAuth,
    }
    return options
}

/**
 * Utility function to execute an API request and return a ServerActionResponse
 * @param props - The FetchClientProps object containing the request details
 * @returns A Promise resolving to a ServerActionResponse object
 * @example
 * const response = await executeApiRequest({
 *     url: "/api/users",
 *     method: "GET",
 * })
 */
export async function executeBaseApiRequest<T>(props: FetchClientProps): Promise<ApiResponse<T>> {
    const response = await fetchClient(props)
    if (!response.ok) {
        const isJson = response.headers
            .get("content-type")
            ?.includes("application/json") ?? false;
        if (!isJson) {
            return {
                success: false,
                message: response.statusText,
                status: response.status,
            }
        }
    }
    const json: ApiResponse<T> = await response.json()
    return json
}


/**
 * Executes a GET API request with optional query parameters and schema validation
 * 
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 *   - params: Optional query parameters as key-value pairs
 *   - schema: Optional Zod schema for validating query parameters
 * 
 * @returns A Promise resolving to a ServerActionResponse containing the API response data or error
 * 
 * @example
 * // Basic GET request
 * const response = await executeGetApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.get
 * })
 * 
 * @example
 * // GET request with query parameters
 * const response = await executeGetApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.get,
 *   params: { role: "admin", status: "active" }
 * })
 */
async function executeGetApiRequest<T>(props: ExecuteGetApiRequestProps): Promise<ApiResponse<T>> {
    let url = props.url

    if (props.params) {
        // Create a copy of the params to avoid mutating the original object
        let queryParams = { ...props.params }

        // Validate params against the provided schema if one exists
        if (props.schema) {
            const parsedParams = props.schema.safeParse(props.params)
            if (!parsedParams.success) {
                // Return validation error if schema validation fails
                return {
                    success: false,
                    message: parsedParams.error.message,
                    status: 400,
                }
            }
            // Cast the parsed data back to the expected type
            queryParams = parsedParams.data as QueryParams
        }
        url = buildUrl(props.url, queryParams)

        // Convert the query parameters object to URLSearchParams format
    }

    // Create a new request options
    const requestOptions: FetchClientProps = {
        url,
        options: props.options,
        withAuth: props.withAuth,
    }

    return executeBaseApiRequest<T>(requestOptions)
}

/**
 * Executes a POST API request with optional schema validation
 * 
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 *   - body: The request body object
 *   - schema: Optional Zod schema for validating the request body
 * 
 * @returns A Promise resolving to a ServerActionResponse containing the API response data or error
 * 
 * @example
 * const response = await executePostApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.post,
 *   body: { name: "John Doe", email: "<EMAIL>" },
 *   schema: z.object({
 *     name: z.string(),
 *     email: z.string().email(),
 *   })
 * })
 */
async function executePostApiRequest<T>(props: ExecutePostApiRequestProps): Promise<ApiResponse<T>> {
    let requestBody = { ...props.body }
    if (props.schema) {
        const parsedBody = props.schema.safeParse(requestBody)
        if (!parsedBody.success) {
            return {
                success: false,
                message: parsedBody.error.message,
                status: 400,
            }
        }
        requestBody = parsedBody.data as PostBody
    }

    const options = getMutationOptions({
        ...props,
        body: requestBody,
    })
    return executeBaseApiRequest<T>(options)
}


/**
 * Executes a PUT API request with optional schema validation
 * 
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 *   - body: The request body object
 *   - schema: Optional Zod schema for validating the request body
 * 
 * @returns A Promise resolving to a ServerActionResponse containing the API response data or error
 * 
 * @example
 * const response = await executePutApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.put,
 *   body: { name: "John Doe", email: "<EMAIL>" },
 *   schema: z.object({
 *     name: z.string(),
 *     email: z.string().email(),
 *   })
 * })
 */
async function executePutApiRequest<T>(props: ExecutePutApiRequestProps): Promise<ApiResponse<T>> {
    let requestBody = { ...props.body }
    if (props.schema) {
        const parsedBody = props.schema.safeParse(requestBody)
        if (!parsedBody.success) {
            return {
                success: false,
                message: parsedBody.error.message,
                status: 400,
            }
        }
        requestBody = parsedBody.data as PostBody
    }
    const options = getMutationOptions({
        ...props,
        body: requestBody
    })
    return executeBaseApiRequest<T>(options)
}

/**
 * Executes a DELETE API request
 * 
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 * 
 * @returns A Promise resolving to a ServerActionResponse containing the API response data or error
 * 
 * @example
 * const response = await executeDeleteApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.delete,
 * })
 */
async function executeDeleteApiRequest<T>(props: ExecuteDeleteApiRequestProps): Promise<ApiResponse<T>> {
    const requestOptions: FetchClientProps = {
        url: props.url,
        options: {
            method: props.method,
            ...props.options,
        },
        withAuth: props.withAuth,
    }
    return executeBaseApiRequest<T>(requestOptions)
}

/**
 * Executes a PATCH API request with optional schema validation
 * 
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 *   - body: The request body object
 *   - schema: Optional Zod schema for validating the request body
 * 
 * @returns A Promise resolving to a ServerActionResponse containing the API response data or error
 * 
 * @example
 * const response = await executePatchApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.patch,
 *   body: { name: "John Doe", email: "<EMAIL>" },
 *   schema: z.object({
 *     name: z.string(),
 *     email: z.string().email(),
 *   })
 * })
 */
async function executePatchApiRequest<T>(props: ExecutePatchApiRequestProps): Promise<ApiResponse<T>> {
    let requestBody = { ...props.body }
    if (props.schema) {
        const parsedBody = props.schema.safeParse(requestBody)
        if (!parsedBody.success) {
            return {
                success: false,
                message: parsedBody.error.message,
                status: 400,
            }
        }
        requestBody = parsedBody.data as PostBody
    }
    const options = getMutationOptions({
        ...props,
        body: requestBody
    })
    return executeBaseApiRequest<T>(options)
}

/**
 * Executes an API request based on the request method
 * 
 * @param props - The request configuration object containing:
 *   - url: The base URL for the API endpoint
 *   - method: The request method
 *   - body: The request body object
 *   - schema: Optional Zod schema for validating the request body
 * 
 * @returns A Promise resolving to a ServerActionResponse containing the API response data or error
 * 
 * @example
 * const response = await executeApiRequest({
 *   url: "/api/users",
 *   method: METHOD_TYPE.post,
 *   body: { name: "John Doe", email: "<EMAIL>" },
 *   schema: z.object({
 *     name: z.string(),
 *     email: z.string().email(),
 *   })
 * })
 */
export async function executeApiRequest<T>(props: ExecuteApiRequestProps): Promise<ApiResponse<T>> {
    switch (props.method) {
        case REQUEST_TYPE.get:
            return executeGetApiRequest<T>(props)
        case REQUEST_TYPE.post:
            return executePostApiRequest<T>(props)
        case REQUEST_TYPE.put:
            return executePutApiRequest<T>(props)
        case REQUEST_TYPE.delete:
            return executeDeleteApiRequest<T>(props)
        case REQUEST_TYPE.patch:
            return executePatchApiRequest<T>(props)
        default:
            throw new Error(`Invalid request method`)
    }
}

