'use client';

import { useEffect, useState } from 'react';
import Loading from '@/components/layouts/loading';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';

interface Note {
    id: number;
    note: string;
    created_at: string;
}

interface AddNoteModalProps {
    service: { id: number; title: string } | null;
    onClose: () => void;
    onConfirm: (note: string) => Promise<void>;
}

export default function AddNoteModal({ service, onClose, onConfirm }: AddNoteModalProps) {
    const [note, setNote] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [loadingNotes, setLoadingNotes] = useState(false);
    const [notes, setNotes] = useState<Note[]>([]);
    const [expandedNoteId, setExpandedNoteId] = useState<number | null>(null);

    if (!service) return null;

    // Fetch notes when modal opens
    useEffect(() => {
        const fetchNotes = async () => {
            if (!service?.id) return;
            try {
                setLoadingNotes(true);
                const res = await fetch(`${API_ENDPOINTS.SERVICES_URL}/note/${service.id}`, { cache: 'no-store', credentials: 'include' });
                const json = await res.json();
                if (!json.success) throw new Error(json.message || 'Failed to fetch notes');
                setNotes(json.data || []);
            } catch (err) {
                console.error(err);
                showMessage('Failed to load notes', 'error');
            } finally {
                setLoadingNotes(false);
            }
        };
        fetchNotes();
    }, [service?.id]);

    const handleSubmit = async () => {
        if (!note.trim()) return;

        setIsSubmitting(true);
        try {
            await onConfirm(note.trim());
            setNote('');
        } finally {
            setIsSubmitting(false);
        }
    };

    const formatDate = (iso: string) => new Date(iso).toLocaleString('en-GB', { year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' });

    return (
        <div className="mx-auto w-full max-w-md rounded-lg bg-white">
            {/* Header */}
            <div className="mb-4 flex items-start justify-between">
                <h2 className="font-inter text-lg font-semibold text-[#2d2d2e]">Service Notes</h2>
                <button onClick={onClose} className="flex h-6 w-6 items-center justify-center rounded-full text-xl text-gray-400 hover:bg-gray-100 hover:text-gray-600">
                    ×
                </button>
            </div>

            {/* Notes List */}
            <div className="mb-6">
                <h3 className="mb-2 text-sm font-medium text-gray-700">Previous Notes</h3>
                {loadingNotes ? (
                    <Loading fullscreen={false} />
                ) : notes.length > 0 ? (
                    <div className="max-h-40 space-y-3 overflow-y-auto">
                        {notes.map((n) => {
                            const isExpanded = expandedNoteId === n.id;
                            const text = isExpanded ? n.note : n.note.slice(0, 50) + (n.note.length > 50 ? '...' : '');

                            return (
                                <div key={n.id} className="rounded-md border border-gray-200 bg-gray-50 p-3">
                                    <p className="text-sm text-gray-700">{text}</p>
                                    <div className="mt-1 flex items-center justify-between text-xs text-gray-500">
                                        <span>{formatDate(n.created_at)}</span>
                                        {n.note.length > 50 && (
                                            <button onClick={() => setExpandedNoteId(isExpanded ? null : n.id)} className="text-blue-600 hover:underline">
                                                {isExpanded ? 'Show less' : 'Load more'}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                ) : (
                    <p className="text-sm text-gray-500">No notes yet for this service.</p>
                )}
            </div>

            {/* Add New Note */}
            <div className="mb-6">
                <label htmlFor="note" className="mb-2 block text-sm font-medium text-gray-700">
                    Add Note *
                </label>
                <textarea
                    id="note"
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                    placeholder="Enter your note here..."
                    className="w-full resize-none rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    required
                />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
                <button
                    onClick={onClose}
                    disabled={isSubmitting}
                    className="rounded border border-gray-300 bg-gray-100 px-4 py-2 font-inter text-sm font-medium text-[#636363] transition-colors hover:bg-gray-200 disabled:opacity-50"
                >
                    Cancel
                </button>
                <button
                    onClick={handleSubmit}
                    disabled={!note.trim() || isSubmitting}
                    className="rounded border border-[#2d2d2e] bg-[#2d2d2e] px-4 py-2 font-inter text-sm font-medium text-white transition-colors hover:bg-black disabled:opacity-50"
                >
                    {isSubmitting ? 'Adding...' : 'Add Note'}
                </button>
            </div>
        </div>
    );
}
